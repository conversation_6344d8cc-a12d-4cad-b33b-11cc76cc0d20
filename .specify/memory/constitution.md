<!-- 
Sync Impact Report:
- Version change: NEW constitution (no previous version) → 1.0.0
- Added principles: 
  * I. Single Responsibility
  * II. CLI Interface
  * III. Test-First (NON-NEGOTIABLE)
  * IV. Contract Testing
  * V. Portability & Simplicity
- Added sections:
  * Technology Constraints
  * Development Workflow
- Removed sections: None (new constitution)
- Templates requiring updates: 
  ✅ plan-template.md (Constitution Check section now applicable)
  ✅ spec-template.md (no changes needed)
  ✅ tasks-template.md (no changes needed)
- Follow-up TODOs: None
-->

# Portable CLI Application Constitution

## Core Principles

### I. Single Responsibility
Each module MUST have one clear purpose and responsibility; Components MUST be independently testable and reusable; No monolithic functions or classes spanning multiple concerns.

### II. CLI Interface
All functionality MUST be accessible via command-line interface; Text-based input/output protocol: stdin/args → stdout, errors → stderr; Support both JSON and human-readable formats for interoperability.

### III. Test-First (NON-NEGOTIABLE)
TDD is mandatory: Tests written → User approved → Tests fail → Then implement; Red-Green-Refactor cycle strictly enforced; No implementation without failing tests.

### IV. Contract Testing
Focus areas requiring contract tests: New command interfaces, API changes, Inter-process communication, Shared data schemas; Tests MUST verify input/output contracts.

### V. Portability & Simplicity
Applications MUST run on any POSIX-compliant system; Dependencies kept to minimum; Start simple, follow YAGNI principles; Avoid platform-specific features unless absolutely necessary.

## Technology Constraints

Portable CLI applications MUST adhere to these constraints:
- Cross-platform compatibility (Linux, macOS, Windows via WSL)
- Minimal external dependencies
- Self-contained binaries when possible
- Standard input/output handling
- Environment variable configuration
- Graceful degradation when optional features unavailable

## Development Workflow

All development follows this workflow:
1. Feature specifications created before implementation
2. Test-first approach with failing tests required
3. Small, focused commits with clear messages
4. Pull requests reviewed for constitutional compliance
5. Integration testing before merging to main branch
6. Version bumping according to semantic versioning rules

## Governance

This Constitution supersedes all other practices; Amendments require documentation, approval, and migration plan; All PRs/reviews must verify constitutional compliance; Complexity must be justified; Use implementation plans for development guidance.

**Version**: 1.0.0 | **Ratified**: 2025-09-23 | **Last Amended**: 2025-09-23