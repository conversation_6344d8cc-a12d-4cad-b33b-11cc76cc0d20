# Media Center Control (mctl)

A modern CLI tool to idempotently manage an ARR-stack consisting of <PERSON><PERSON>, Sonarr, <PERSON>wlarr, <PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON>, Transmission, unpackerr, and Overseerr.

## Features

- **Prerequisite Checking**: Automatically checks for required system components
- **Guided Setup**: Helps install missing prerequisites
- **Code-based Configuration**: All configuration managed through TOML files
- **Idempotent Operations**: Safe to run commands multiple times
- **Dependency Management**: Automatically handles application startup order
- **Full Observability**: Comprehensive logging, metrics, and health checks

## Installation

### From Source

```bash
git clone https://github.com/idriesalbender/media-center-v3.git
cd media-center-v3
go build -o mctl cmd/mctl/main.go
```

### From Release

Download the latest release binary for your platform from the [releases page](https://github.com/idriesalbender/media-center-v3/releases).

## Quick Start

1. **Initialize Configuration**:
   ```bash
   mctl init
   ```

2. **Start the Stack**:
   ```bash
   mctl start
   ```

3. **Check Status**:
   ```bash
   mctl status
   ```

4. **Stop the Stack**:
   ```bash
   mctl stop
   ```

## Commands

### `init`
Initialize the ARR-stack configuration and check prerequisites.

```bash
mctl init [--config-dir PATH] [--force]
```

Options:
- `--config-dir PATH`: Path to the configuration directory (default: ~/.config/mctl)
- `--force`: Force reinitialization even if configuration already exists

### `start`
Start the ARR-stack applications.

```bash
mctl start [--applications APP1,APP2]
```

Options:
- `--applications APP1,APP2`: Comma-separated list of applications to start

### `stop`
Stop the ARR-stack applications.

```bash
mctl stop [--applications APP1,APP2]
```

Options:
- `--applications APP1,APP2`: Comma-separated list of applications to stop

### `status`
Show the status of ARR-stack applications.

```bash
mctl status [--format table|json] [--applications APP1,APP2]
```

Options:
- `--format table|json`: Output format (default: table)
- `--applications APP1,APP2`: Comma-separated list of applications to check status for

## Configuration

The default configuration file is created at `~/.config/mctl/config.toml`. Key sections include:

```toml
version = "1.0.0"

[global]
data_directory = "/path/to/media"
config_directory = "/path/to/config"
backup_directory = "/path/to/backups"
timezone = "America/New_York"

[observability]
log_level = "info"
log_directory = "/path/to/logs"
metrics_enabled = true
health_check_interval = 30

[[applications]]
name = "radarr"
type = "radarr"
enabled = true
image = "lscr.io/linuxserver/radarr"
version = "latest"
depends_on = []

[applications.health_check]
endpoint = "/api/v3/health"
port = 7878
timeout = 10
retries = 3

[[applications.ports]]
host_port = 7878
container_port = 7878
protocol = "tcp"

[[applications.volumes]]
host_path = "/path/to/movies"
container_path = "/movies"
read_only = false

[[applications.volumes]]
host_path = "/path/to/config/radarr"
container_path = "/config"
read_only = false

[[applications.environment]]
PUID = "1000"
PGID = "1000"
TZ = "America/New_York"
```

## Prerequisites

- Docker installed and running
- Docker Compose available
- At least 4GB RAM available for the full stack

## Supported Applications

- **Radarr**: Movie collection manager
- **Sonarr**: TV series collection manager
- **Prowlarr**: Indexer manager
- **Bazarr**: Subtitle manager
- **Autobrr**: Modern download automation
- **Jellyfin**: Media server
- **Transmission**: BitTorrent client
- **unpackerr**: Unpacks archives
- **Overseerr**: Media request management

## Troubleshooting

### Docker Not Running
```
Error: Docker daemon not accessible
Solution: Start Docker Desktop or ensure Docker service is running
```

### Port Conflicts
```
Error: Port 7878 already in use
Solution: Change the host port in the configuration file or stop the conflicting service
```

### Image Pull Failures
```
Error: Failed to pull image lscr.io/linuxserver/radarr
Solution: Check internet connectivity or verify the image name in configuration
```

## Development

### Building

```bash
go build -o mctl cmd/mctl/main.go
```

### Testing

```bash
# Run unit tests
go test ./tests/unit/...

# Run integration tests
go test ./tests/integration/...

# Run contract tests
go test ./tests/contract/...

# Run performance tests
go test ./tests/performance/...
```

### Dependencies

- Go 1.21+
- Kong CLI framework
- Docker SDK
- TOML parser
- Testify for testing

## License

MIT