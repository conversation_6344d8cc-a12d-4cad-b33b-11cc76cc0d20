package main

import (
	"github.com/alecthomas/kong"
	"github.com/idriesalbender/media-center-v3/src/cli"
)

// CLI represents the main command line interface
var CLI struct {
	Init   cli.InitCmd   `cmd:"" help:"Initialize the ARR-stack configuration"`
	Start  cli.StartCmd  `cmd:"" help:"Start the ARR-stack applications"`
	Stop   cli.StopCmd   `cmd:"" help:"Stop the ARR-stack applications"`
	Status cli.StatusCmd `cmd:"" help:"Show the status of ARR-stack applications"`
}

func main() {
	// Parse command line arguments and execute the appropriate command
	ctx := kong.Parse(&CLI)
	err := ctx.Run()
	ctx.FatalIfErrorf(err)
}