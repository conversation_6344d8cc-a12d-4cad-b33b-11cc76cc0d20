# Example Configuration for Media Center Control (mctl)

version = "1.0.0"

[global]
# Base directory for application data
data_directory = "/home/<USER>/media"

# Base directory for configuration files
config_directory = "/home/<USER>/.config/mctl/apps"

# Directory for backups
backup_directory = "/home/<USER>/media/backups"

# Timezone for applications
timezone = "America/New_York"

[observability]
# Logging level (debug, info, warn, error)
log_level = "info"

# Directory for log files
log_directory = "/home/<USER>/.local/share/mctl/logs"

# Whether to collect metrics
metrics_enabled = true

# Interval in seconds for health checks
health_check_interval = 30

# Radarr configuration
[[applications]]
name = "radarr"
type = "radarr"
enabled = true
image = "lscr.io/linuxserver/radarr"
version = "latest"
depends_on = []

[applications.health_check]
endpoint = "/api/v3/health"
port = 7878
timeout = 10
retries = 3

[[applications.ports]]
host_port = 7878
container_port = 7878
protocol = "tcp"

[[applications.volumes]]
host_path = "/home/<USER>/media/movies"
container_path = "/movies"
read_only = false

[[applications.volumes]]
host_path = "/home/<USER>/.config/mctl/apps/radarr"
container_path = "/config"
read_only = false

[[applications.environment]]
PUID = "1000"
PGID = "1000"
TZ = "America/New_York"

# Sonarr configuration
[[applications]]
name = "sonarr"
type = "sonarr"
enabled = true
image = "lscr.io/linuxserver/sonarr"
version = "latest"
depends_on = []

[applications.health_check]
endpoint = "/api/v3/health"
port = 8989
timeout = 10
retries = 3

[[applications.ports]]
host_port = 8989
container_port = 8989
protocol = "tcp"

[[applications.volumes]]
host_path = "/home/<USER>/media/tv"
container_path = "/tv"
read_only = false

[[applications.volumes]]
host_path = "/home/<USER>/.config/mctl/apps/sonarr"
container_path = "/config"
read_only = false

[[applications.environment]]
PUID = "1000"
PGID = "1000"
TZ = "America/New_York"

# Prowlarr configuration
[[applications]]
name = "prowlarr"
type = "prowlarr"
enabled = true
image = "lscr.io/linuxserver/prowlarr"
version = "latest"
depends_on = []

[applications.health_check]
endpoint = "/health"
port = 9696
timeout = 10
retries = 3

[[applications.ports]]
host_port = 9696
container_port = 9696
protocol = "tcp"

[[applications.volumes]]
host_path = "/home/<USER>/.config/mctl/apps/prowlarr"
container_path = "/config"
read_only = false

[[applications.environment]]
PUID = "1000"
PGID = "1000"
TZ = "America/New_York"

# Bazarr configuration
[[applications]]
name = "bazarr"
type = "bazarr"
enabled = true
image = "lscr.io/linuxserver/bazarr"
version = "latest"
depends_on = ["sonarr", "radarr"]

[applications.health_check]
endpoint = "/health"
port = 6767
timeout = 10
retries = 3

[[applications.ports]]
host_port = 6767
container_port = 6767
protocol = "tcp"

[[applications.volumes]]
host_path = "/home/<USER>/media/movies"
container_path = "/movies"
read_only = false

[[applications.volumes]]
host_path = "/home/<USER>/media/tv"
container_path = "/tv"
read_only = false

[[applications.volumes]]
host_path = "/home/<USER>/.config/mctl/apps/bazarr"
container_path = "/config"
read_only = false

[[applications.environment]]
PUID = "1000"
PGID = "1000"
TZ = "America/New_York"

# Jellyfin configuration
[[applications]]
name = "jellyfin"
type = "jellyfin"
enabled = true
image = "lscr.io/linuxserver/jellyfin"
version = "latest"
depends_on = []

[[applications.ports]]
host_port = 8096
container_port = 8096
protocol = "tcp"

[[applications.volumes]]
host_path = "/home/<USER>/media"
container_path = "/media"
read_only = false

[[applications.volumes]]
host_path = "/home/<USER>/.config/mctl/apps/jellyfin"
container_path = "/config"
read_only = false

[[applications.volumes]]
host_path = "/home/<USER>/.local/share/jellyfin/cache"
container_path = "/cache"
read_only = false

[[applications.environment]]
PUID = "1000"
PGID = "1000"
TZ = "America/New_York"