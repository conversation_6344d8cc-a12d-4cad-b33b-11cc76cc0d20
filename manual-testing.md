# Manual Testing Scenarios

This document outlines the manual testing scenarios to validate the mctl CLI functionality.

## Prerequisites

Before running these tests, ensure:
1. <PERSON><PERSON> is installed and running
2. Docker Compose is available
3. The mctl binary has been built

## Test Scenarios

### 1. Initialize Configuration

**Scenario**: User initializes the ARR-stack configuration with default settings

**Steps**:
1. Run `mctl init`
2. Verify the command completes successfully
3. Check that the configuration file is created at `~/.config/mctl/config.toml`
4. Verify the configuration file contains valid TOML syntax
5. Verify prerequisite checks are performed and reported

**Expected Output**:
```
✓ Docker installed: version X.X.X
✓ Docker daemon running
✓ Docker Compose available: version X.X.X
✓ Configuration created at ~/.config/mctl/config.toml
ARR-stack initialized successfully
```

### 2. Initialize Configuration with Custom Directory

**Scenario**: User initializes the ARR-stack configuration with a custom directory

**Steps**:
1. Run `mctl init --config-dir /tmp/mctl-test`
2. Verify the command completes successfully
3. Check that the configuration file is created at `/tmp/mctl-test/config.toml`
4. Verify the configuration file contains valid TOML syntax

**Expected Output**:
```
✓ Docker installed: version X.X.X
✓ Docker daemon running
✓ Docker Compose available: version X.X.X
✓ Configuration created at /tmp/mctl-test/config.toml
ARR-stack initialized successfully
```

### 3. Force Reinitialization

**Scenario**: User forces reinitialization of existing configuration

**Steps**:
1. Run `mctl init` (first time)
2. Verify the configuration is created
3. Run `mctl init --force`
4. Verify the command completes successfully
5. Check that the configuration file is overwritten

**Expected Output**:
```
✓ Docker installed: version X.X.X
✓ Docker daemon running
✓ Docker Compose available: version X.X.X
✓ Configuration created at ~/.config/mctl/config.toml
ARR-stack initialized successfully
```

### 4. Start the Stack

**Scenario**: User starts all applications in the ARR-stack

**Steps**:
1. Ensure configuration is initialized
2. Run `mctl start`
3. Verify the command completes successfully
4. Check that Docker containers are started for each application
5. Verify applications are started in the correct dependency order

**Expected Output**:
```
Starting applications in dependency order...
✓ radarr: started (container: abc123def456)
✓ sonarr: started (container: ghi789jkl012)
✓ prowlarr: started (container: mno345pqr678)
ARR-stack started successfully
```

### 5. Start Specific Applications

**Scenario**: User starts only specific applications

**Steps**:
1. Ensure configuration is initialized
2. Run `mctl start --applications radarr,sonarr`
3. Verify the command completes successfully
4. Check that only radarr and sonarr containers are started
5. Verify applications are started in the correct dependency order

**Expected Output**:
```
Starting applications in dependency order...
✓ radarr: started (container: abc123def456)
✓ sonarr: started (container: ghi789jkl012)
ARR-stack started successfully
```

### 6. Check Status (Table Format)

**Scenario**: User checks the status of all applications in table format

**Steps**:
1. Ensure some applications are running
2. Run `mctl status`
3. Verify the command completes successfully
4. Check that the output is in table format
5. Verify that status information is accurate

**Expected Output**:
```
Application    Status     Container ID      Health
-----------    ------     ------------      ------
radarr         running    abc123def456      healthy
sonarr         running    ghi789jkl012      healthy
prowlarr       running    mno345pqr678      healthy
bazarr         stopped    -                 -
```

### 7. Check Status (JSON Format)

**Scenario**: User checks the status of all applications in JSON format

**Steps**:
1. Ensure some applications are running
2. Run `mctl status --format json`
3. Verify the command completes successfully
4. Check that the output is in valid JSON format
5. Verify that status information is accurate

**Expected Output**:
```json
{
  "status": "success",
  "applications": [
    {
      "name": "radarr",
      "status": "running",
      "container_id": "abc123def456",
      "health": "healthy"
    },
    {
      "name": "sonarr",
      "status": "running",
      "container_id": "ghi789jkl012",
      "health": "healthy"
    }
  ]
}
```

### 8. Stop the Stack

**Scenario**: User stops all applications in the ARR-stack

**Steps**:
1. Ensure some applications are running
2. Run `mctl stop`
3. Verify the command completes successfully
4. Check that Docker containers are stopped for each application
5. Verify applications are stopped in the correct dependency order

**Expected Output**:
```
Stopping applications in dependency order...
✓ prowlarr: stopped
✓ sonarr: stopped
✓ radarr: stopped
ARR-stack stopped successfully
```

### 9. Stop Specific Applications

**Scenario**: User stops only specific applications

**Steps**:
1. Ensure some applications are running
2. Run `mctl stop --applications radarr,sonarr`
3. Verify the command completes successfully
4. Check that only radarr and sonarr containers are stopped
5. Verify applications are stopped in the correct dependency order

**Expected Output**:
```
Stopping applications in dependency order...
✓ sonarr: stopped
✓ radarr: stopped
ARR-stack stopped successfully
```

### 10. Error Handling - Docker Not Running

**Scenario**: User attempts to use mctl when Docker is not running

**Steps**:
1. Stop the Docker daemon
2. Run `mctl init`
3. Verify the command fails with an appropriate error message

**Expected Output**:
```
Error: Docker daemon not accessible
Solution: Start Docker Desktop or ensure Docker service is running
```

### 11. Error Handling - Invalid Configuration

**Scenario**: User attempts to start the stack with an invalid configuration file

**Steps**:
1. Modify the configuration file to make it invalid TOML
2. Run `mctl start`
3. Verify the command fails with an appropriate error message

**Expected Output**:
```
Error: Configuration error: Invalid TOML syntax
Solution: Please check your configuration file and correct the syntax errors
```

## Cleanup

After testing, clean up any created resources:
1. Stop any running containers: `mctl stop`
2. Remove test configuration directories
3. Remove test data directories