# Init Command Contract

## Command
`mctl init`

## Description
Initialize the ARR-stack configuration and check prerequisites.

## Input
- Configuration directory path (optional, defaults to ~/.config/mctl)
- Force reinitialization flag (optional)

## Output (Success)
```json
{
  "status": "success",
  "message": "ARR-stack initialized successfully",
  "config_path": "/path/to/config",
  "prerequisites": {
    "docker_installed": true,
    "docker_running": true,
    "docker_compose_available": true
  }
}
```

## Output (Failure - Prerequisites Missing)
```json
{
  "status": "error",
  "message": "Prerequisites check failed",
  "missing": ["docker", "docker-compose"],
  "instructions": [
    "Install Docker Desktop from https://www.docker.com/products/docker-desktop",
    "Ensure Docker daemon is running"
  ]
}
```

## Output (Failure - Other)
```json
{
  "status": "error",
  "message": "Failed to initialize configuration",
  "details": "Error details here"
}
```

## Error Conditions
- Docker not installed
- Docker daemon not running
- Docker Compose not available
- Insufficient permissions to create config directory
- Configuration directory not writable