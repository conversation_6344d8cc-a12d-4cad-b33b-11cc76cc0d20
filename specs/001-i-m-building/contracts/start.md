# Start Command Contract

## Command
`mctl start`

## Description
Start all enabled applications in the ARR-stack in the correct dependency order.

## Input
- Configuration file path (optional, defaults to ~/.config/mctl/config.toml)
- Application filter (optional, to start specific applications only)

## Output (Success)
```json
{
  "status": "success",
  "message": "ARR-stack started successfully",
  "applications": [
    {
      "name": "radarr",
      "status": "running",
      "container_id": "abc123def456"
    },
    {
      "name": "sonarr",
      "status": "running",
      "container_id": "ghi789jkl012"
    }
  ]
}
```

## Output (Partial Success)
```json
{
  "status": "partial",
  "message": "Some applications failed to start",
  "applications": [
    {
      "name": "radarr",
      "status": "running",
      "container_id": "abc123def456"
    },
    {
      "name": "sonarr",
      "status": "error",
      "error": "Port 8989 already in use"
    }
  ]
}
```

## Output (Failure)
```json
{
  "status": "error",
  "message": "Failed to start ARR-stack",
  "details": "Error details here"
}
```

## Error Conditions
- Configuration file not found or invalid
- Docker daemon not accessible
- Insufficient system resources
- Port conflicts
- Image pull failures
- Container startup failures
- Dependency resolution failures