# Status Command Contract

## Command
`mctl status`

## Description
Provide current status of all applications in the ARR-stack.

## Input
- Configuration file path (optional, defaults to ~/.config/mctl/config.toml)
- Application filter (optional, to check status of specific applications only)
- Output format (optional, json or table, defaults to table)

## Output (Success - Table Format)
```
Application    Status     Container ID      Health
-----------    ------     ------------      ------
radarr         running    abc123def456      healthy
sonarr         stopped    -                 -
prowlarr       running    ghi789jkl012      healthy
```

## Output (Success - JSON Format)
```json
{
  "status": "success",
  "applications": [
    {
      "name": "radarr",
      "status": "running",
      "container_id": "abc123def456",
      "health": "healthy",
      "ports": [
        {
          "host": 7878,
          "container": 7878
        }
      ]
    },
    {
      "name": "sonarr",
      "status": "stopped",
      "container_id": "",
      "health": "unknown"
    }
  ]
}
```

## Output (Failure)
```json
{
  "status": "error",
  "message": "Failed to retrieve status",
  "details": "Error details here"
}
```

## Error Conditions
- Configuration file not found or invalid
- Docker daemon not accessible
- Container inspection failures