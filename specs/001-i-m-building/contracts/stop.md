# Stop Command Contract

## Command
`mctl stop`

## Description
Stop all running applications in the ARR-stack in the correct dependency order.

## Input
- Configuration file path (optional, defaults to ~/.config/mctl/config.toml)
- Application filter (optional, to stop specific applications only)

## Output (Success)
```json
{
  "status": "success",
  "message": "ARR-stack stopped successfully",
  "applications": [
    {
      "name": "radarr",
      "status": "stopped",
      "container_id": "abc123def456"
    },
    {
      "name": "sonarr",
      "status": "stopped",
      "container_id": "ghi789jkl012"
    }
  ]
}
```

## Output (Partial Success)
```json
{
  "status": "partial",
  "message": "Some applications failed to stop",
  "applications": [
    {
      "name": "radarr",
      "status": "stopped",
      "container_id": "abc123def456"
    },
    {
      "name": "sonarr",
      "status": "error",
      "error": "Container not found"
    }
  ]
}
```

## Output (Failure)
```json
{
  "status": "error",
  "message": "Failed to stop ARR-stack",
  "details": "Error details here"
}
```

## Error Conditions
- Configuration file not found or invalid
- Docker daemon not accessible
- Container stop failures
- Dependency resolution failures