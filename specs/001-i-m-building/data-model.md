# Data Model: ARR-Stack Management CLI

**Feature**: Modern CLI for ARR-Stack Management
**Date**: 2025-09-23

## Entities

### ARRStackConfiguration
Represents the code-based configuration for the entire ARR-stack in TOML format.

**Fields**:
- `version` (string): Configuration schema version
- `applications` (list of ApplicationConfig): List of configured applications
- `global_settings` (GlobalSettings): Global configuration settings
- `observability` (ObservabilityConfig): Observability configuration

**Validation Rules**:
- Version must be a valid semantic version
- At least one application must be configured
- Application names must be unique
- Required fields must be present for each application type

### ApplicationConfig
Represents the configuration for a single application in the stack.

**Fields**:
- `name` (string): Unique identifier for the application
- `type` (string): Type of application (radarr, sonarr, prowlarr, etc.)
- `enabled` (boolean): Whether the application should be managed
- `image` (string): Docker image to use
- `version` (string): Version/tag of the Docker image
- `ports` (list of PortMapping): Port mappings for the container
- `volumes` (list of VolumeMapping): Volume mappings for the container
- `environment` (map of string to string): Environment variables
- `depends_on` (list of string): Names of applications this one depends on
- `health_check` (HealthCheckConfig): Health check configuration

**Validation Rules**:
- Name must be unique within the stack
- Type must be one of the supported application types
- Image must be a valid Docker image reference
- Port mappings must not conflict with system reserved ports
- Volume mappings must reference valid host paths

### PortMapping
Represents a mapping between host and container ports.

**Fields**:
- `host_port` (int): Port on the host system
- `container_port` (int): Port inside the container
- `protocol` (string): Network protocol (tcp, udp)

**Validation Rules**:
- Host port must be in valid range (1-65535)
- Container port must be in valid range (1-65535)
- Protocol must be either "tcp" or "udp"

### VolumeMapping
Represents a mapping between host and container file system paths.

**Fields**:
- `host_path` (string): Path on the host system
- `container_path` (string): Path inside the container
- `read_only` (boolean): Whether the volume should be read-only

**Validation Rules**:
- Host path must be an absolute path
- Container path must be an absolute path
- Host path must exist or be creatable

### GlobalSettings
Represents global configuration settings for the stack.

**Fields**:
- `data_directory` (string): Base directory for application data
- `config_directory` (string): Base directory for configuration files
- `backup_directory` (string): Directory for backups
- `timezone` (string): Timezone for applications

**Validation Rules**:
- All directory paths must be absolute
- Timezone must be a valid tz database identifier

### ObservabilityConfig
Represents observability configuration for the stack.

**Fields**:
- `log_level` (string): Logging level (debug, info, warn, error)
- `log_directory` (string): Directory for log files
- `metrics_enabled` (boolean): Whether to collect metrics
- `health_check_interval` (int): Interval in seconds for health checks

**Validation Rules**:
- Log level must be one of the supported levels
- Log directory must be an absolute path
- Health check interval must be positive

### HealthCheckConfig
Represents health check configuration for an application.

**Fields**:
- `endpoint` (string): HTTP endpoint to check
- `port` (int): Port to check (if different from main port)
- `timeout` (int): Timeout in seconds
- `retries` (int): Number of retries before marking as failed

**Validation Rules**:
- Endpoint must be a valid HTTP path
- Port must be in valid range (1-65535)
- Timeout must be positive
- Retries must be non-negative

### ApplicationState
Represents the current running state of an application.

**Fields**:
- `name` (string): Name of the application
- `status` (string): Current status (running, stopped, error, starting, stopping)
- `container_id` (string): Docker container ID (if applicable)
- `last_updated` (timestamp): When the status was last updated
- `error_message` (string): Error message if in error state

**Validation Rules**:
- Status must be one of the defined status values
- Container ID must be a valid Docker container ID format if present
- Last updated must be a valid timestamp

### Prerequisites
Represents the system prerequisites needed to run the ARR-stack.

**Fields**:
- `docker_installed` (boolean): Whether Docker is installed
- `docker_running` (boolean): Whether Docker daemon is running
- `docker_compose_available` (boolean): Whether Docker Compose is available
- `system_resources` (SystemResources): Available system resources

### SystemResources
Represents available system resources.

**Fields**:
- `total_memory_mb` (int): Total system memory in MB
- `available_memory_mb` (int): Available memory in MB
- `total_disk_gb` (int): Total disk space in GB
- `available_disk_gb` (int): Available disk space in GB

## Relationships

1. **ARRStackConfiguration** contains many **ApplicationConfig**
2. **ApplicationConfig** contains many **PortMapping**
3. **ApplicationConfig** contains many **VolumeMapping**
4. **ApplicationConfig** has one **HealthCheckConfig**
5. **ARRStackConfiguration** has one **GlobalSettings**
6. **ARRStackConfiguration** has one **ObservabilityConfig**
7. **ARRStackConfiguration** tracks many **ApplicationState**
8. **SystemResources** is part of **Prerequisites**

## State Transitions

### ApplicationState Transitions
```
[Stopped] → (start) → [Starting] → (success) → [Running]
                    ↘ (failure) ↘ [Error]

[Running] → (stop) → [Stopping] → (success) → [Stopped]
                   ↘ (failure) ↘ [Error]

[Error] → (retry/start) → [Starting]
[Error] → (stop) → [Stopping] → [Stopped]
```