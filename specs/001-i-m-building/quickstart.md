# Quick Start Guide: ARR-Stack Management CLI

**Feature**: Modern CLI for ARR-Stack Management
**Tool Name**: mctl (media center control)
**Date**: 2025-09-23

## Prerequisites
- Docker installed and running
- Docker Compose available
- Go 1.21+ (for development only)

## Installation
```bash
# Download the latest release binary for your platform
# Or build from source:
git clone <repository-url>
cd <project-directory>
go build -o mctl cmd/mctl/main.go
```

## Quick Start Steps

### 1. Initialize Configuration
```bash
# Initialize with default settings
mctl init

# Initialize with custom config directory
mctl init --config-dir /path/to/config
```

Expected output:
```
✓ Docker installed: version 20.10.21
✓ Docker daemon running
✓ Docker Compose available: version 2.12.2
✓ Configuration created at /Users/<USER>/.config/mctl/config.toml
ARR-stack initialized successfully
```

### 2. Review Configuration
```bash
# View the generated configuration
cat ~/.config/mctl/config.toml
```

### 3. Start the Stack
```bash
# Start all applications
mctl start
```

Expected output:
```
Starting applications in dependency order...
✓ radarr: started (container: abc123def456)
✓ sonarr: started (container: ghi789jkl012)
✓ prowlarr: started (container: mno345pqr678)
ARR-stack started successfully
```

### 4. Check Status
```bash
# Check the status of all applications
mctl status
```

Expected output:
```
Application    Status     Container ID      Health
-----------    ------     ------------      ------
radarr         running    abc123def456      healthy
sonarr         running    ghi789jkl012      healthy
prowlarr       running    mno345pqr678      healthy
bazarr         stopped    -                 -
```

### 5. Stop the Stack
```bash
# Stop all applications
mctl stop
```

Expected output:
```
Stopping applications in dependency order...
✓ prowlarr: stopped
✓ sonarr: stopped
✓ radarr: stopped
ARR-stack stopped successfully
```

## Common Tasks

### Start Specific Applications
```bash
# Start only radarr and sonarr
mctl start --applications radarr,sonarr
```

### Get JSON Status
```bash
# Get status in JSON format for scripting
mctl status --format json
```

### Reinitialize Configuration
```bash
# Force reinitialization
mctl init --force
```

## Configuration

The default configuration file is created at `~/.config/mctl/config.toml`. Key sections include:

```toml
version = "1.0.0"

[global]
data_directory = "/Users/<USER>/media"
config_directory = "/Users/<USER>/.config/mctl/apps"
backup_directory = "/Users/<USER>/media/backups"
timezone = "America/New_York"

[observability]
log_level = "info"
log_directory = "/Users/<USER>/.local/share/mctl/logs"
metrics_enabled = true
health_check_interval = 30

[[applications]]
name = "radarr"
type = "radarr"
enabled = true
image = "lscr.io/linuxserver/radarr"
version = "latest"
depends_on = []

[applications.health_check]
endpoint = "/api/v3/health"
port = 7878
timeout = 10
retries = 3

[[applications.ports]]
host_port = 7878
container_port = 7878
protocol = "tcp"

[[applications.volumes]]
host_path = "/Users/<USER>/media/movies"
container_path = "/movies"
read_only = false

[[applications.volumes]]
host_path = "/Users/<USER>/.config/mctl/apps/radarr"
container_path = "/config"
read_only = false

[[applications.environment]]
PUID = "1000"
PGID = "1000"
TZ = "America/New_York"
```

## Troubleshooting

### Docker Not Running
```
Error: Docker daemon not accessible
Solution: Start Docker Desktop or ensure Docker service is running
```

### Port Conflicts
```
Error: Port 7878 already in use
Solution: Change the host port in the configuration file or stop the conflicting service
```

### Image Pull Failures
```
Error: Failed to pull image lscr.io/linuxserver/radarr
Solution: Check internet connectivity or verify the image name in configuration
```

## Next Steps
1. Customize the configuration file to match your media directory structure
2. Add additional applications to the stack
3. Set up automated backups
4. Configure notifications and alerts