# Tasks: Modern CLI for ARR-Stack Management

**Input**: Design documents from `/specs/001-i-m-building/`
**Prerequisites**: plan.md (required), research.md, data-model.md, contracts/

## Execution Flow (main)
```
1. Load plan.md from feature directory
   → If not found: ERROR "No implementation plan found"
   → Extract: tech stack, libraries, structure
2. Load optional design documents:
   → data-model.md: Extract entities → model tasks
   → contracts/: Each file → contract test task
   → research.md: Extract decisions → setup tasks
3. Generate tasks by category:
   → Setup: project init, dependencies, linting
   → Tests: contract tests, integration tests
   → Core: models, services, CLI commands
   → Integration: DB, middleware, logging
   → Polish: unit tests, performance, docs
4. Apply task rules:
   → Different files = mark [P] for parallel
   → Same file = sequential (no [P])
   → Tests before implementation (TDD)
5. Number tasks sequentially (T001, T002...)
6. Generate dependency graph
7. Create parallel execution examples
8. Validate task completeness:
   → All contracts have tests?
   → All entities have models?
   → All endpoints implemented?
9. Return: SUCCESS (tasks ready for execution)
```

## Format: `[ID] [P?] Description`
- **[P]**: Can run in parallel (different files, no dependencies)
- Include exact file paths in descriptions

## Path Conventions
- **Single project**: `src/`, `tests/` at repository root
- **Web app**: `backend/src/`, `frontend/src/`
- **Mobile**: `api/src/`, `ios/src/` or `android/src/`
- Paths shown below assume single project - adjust based on plan.md structure

## Phase 3.1: Setup
- [x] T001 Create project structure per implementation plan in repository root
- [x] T002 Initialize Go project with Kong CLI framework dependencies in go.mod
- [x] T003 [P] Configure linting and formatting tools (golangci-lint) in .golangci.yml

## Phase 3.2: Tests First (TDD) ⚠️ MUST COMPLETE BEFORE 3.3
**CRITICAL: These tests MUST be written and MUST FAIL before ANY implementation**
- [x] T004 [P] Contract test for init command in tests/contract/test_init_command.go
- [x] T005 [P] Contract test for start command in tests/contract/test_start_command.go
- [x] T006 [P] Contract test for stop command in tests/contract/test_stop_command.go
- [x] T007 [P] Contract test for status command in tests/contract/test_status_command.go
- [x] T008 [P] Contract test for TOML configuration parsing in tests/contract/test_toml_config.go
- [x] T009 [P] Integration test for quickstart scenario 1 (init) in tests/integration/test_quickstart_init.go
- [x] T010 [P] Integration test for quickstart scenario 3 (start) in tests/integration/test_quickstart_start.go
- [x] T011 [P] Integration test for quickstart scenario 4 (status) in tests/integration/test_quickstart_status.go
- [x] T012 [P] Integration test for quickstart scenario 5 (stop) in tests/integration/test_quickstart_stop.go

## Phase 3.3: Core Implementation (ONLY after tests are failing)
- [x] T013 [P] ARRStackConfiguration model in src/models/arr_stack_config.go
- [x] T014 [P] ApplicationConfig model in src/models/application_config.go
- [x] T015 [P] PortMapping model in src/models/port_mapping.go
- [x] T016 [P] VolumeMapping model in src/models/volume_mapping.go
- [x] T017 [P] GlobalSettings model in src/models/global_settings.go
- [x] T018 [P] ObservabilityConfig model in src/models/observability_config.go
- [x] T019 [P] HealthCheckConfig model in src/models/health_check_config.go
- [x] T020 [P] ApplicationState model in src/models/application_state.go
- [x] T021 [P] Prerequisites model in src/models/prerequisites.go
- [x] T022 [P] SystemResources model in src/models/system_resources.go
- [x] T023 [P] Implement Configuration service in src/services/config_service.go
- [x] T024 [P] Implement Docker service in src/services/docker_service.go
- [x] T025 [P] Implement Application state service in src/services/app_state_service.go
- [x] T026 [P] Implement Prerequisites checker service in src/services/prerequisites_service.go
- [x] T027 [P] Implement Init command CLI handler in src/cli/init_command.go
- [x] T028 [P] Implement Start command CLI handler in src/cli/start_command.go
- [x] T029 [P] Implement Stop command CLI handler in src/cli/stop_command.go
- [x] T030 [P] Implement Status command CLI handler in src/cli/status_command.go
- [x] T031 Main CLI entry point in cmd/mctl/main.go
- [x] T032 Implement Configuration file parser in src/lib/config_parser.go
- [x] T033 Implement TOML serializer/deserializer in src/lib/toml_handler.go

## Phase 3.4: Integration
- [ ] T034 Connect Docker service to Docker SDK and implement application dependency resolution in src/services/docker_service.go
- [x] T035 Add logging middleware and implement health check functionality in src/lib/logging.go
- [x] T036 Add error handling and user-friendly messages across all services
- [x] T037 Implement cross-platform path handling in src/lib/path_utils.go

## Phase 3.5: Polish
- [x] T038 [P] Unit tests for configuration validation in tests/unit/test_config_validation.go
- [x] T039 [P] Unit tests for Docker service in tests/unit/test_docker_service.go
- [x] T040 [P] Unit tests for application state transitions in tests/unit/test_app_state.go
- [x] T041 Performance tests for all commands (status <1 second, start <10 seconds, stop <5 seconds, init <3 seconds) in tests/performance/test_command_perf.go
- [x] T042 [P] Update documentation in README.md
- [x] T043 [P] Create example configuration file in examples/config.toml
- [x] T044 Remove duplication and refactor as needed
- [x] T045 Run manual-testing.md scenarios

## Dependencies
- Tests (T004-T012) before implementation (T013-T033)
- Model tasks (T013-T022) before service tasks (T023-T026)
- Service tasks (T023-T026) before CLI command tasks (T027-T030)
- CLI commands (T027-T030) before main entry point (T031)
- Core implementation (T013-T033) before integration (T034-T037)
- Integration (T034-T037) before polish (T038-T045)

## Parallel Example
```
# Launch T023-T026 together:
Task: "Implement Configuration service in src/services/config_service.go"
Task: "Implement Docker service in src/services/docker_service.go"
Task: "Implement Application state service in src/services/app_state_service.go"
Task: "Implement Prerequisites checker service in src/services/prerequisites_service.go"

# Launch T027-T030 together:
Task: "Implement Init command CLI handler in src/cli/init_command.go"
Task: "Implement Start command CLI handler in src/cli/start_command.go"
Task: "Implement Stop command CLI handler in src/cli/stop_command.go"
Task: "Implement Status command CLI handler in src/cli/status_command.go"
```

## Notes
- [P] tasks = different files, no dependencies
- Verify tests fail before implementing
- Commit after each task
- Avoid: vague tasks, same file conflicts

## Task Generation Rules
*Applied during main() execution*

1. **From Contracts**:
   - Each contract file → contract test task [P]
   - Each endpoint → implementation task
   
2. **From Data Model**:
   - Each entity → model creation task [P]
   - Relationships → service layer tasks
   
3. **From User Stories**:
   - Each story → integration test [P]
   - Quickstart scenarios → validation tasks

4. **Ordering**:
   - Setup → Tests → Models → Services → Endpoints → Polish
   - Dependencies block parallel execution

## Validation Checklist
*GATE: Checked by main() before returning*

- [x] All contracts have corresponding tests (5 contracts → 5 tests)
- [x] All entities have model tasks (9 entities → 9 model tasks)
- [x] All tests come before implementation
- [x] Parallel tasks truly independent
- [x] Each task specifies exact file path
- [x] No task modifies same file as another [P] task