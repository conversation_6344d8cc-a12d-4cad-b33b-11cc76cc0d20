package cli

import (
	"fmt"
	"os"
	"path/filepath"

	"github.com/alecthomas/kong"
	"github.com/idriesalbender/media-center-v3/src/lib"
	"github.com/idriesalbender/media-center-v3/src/services"
)

// InitCmd represents the init command
type InitCmd struct {
	// ConfigDir is the path to the configuration directory
	ConfigDir string `name:"config-dir" help:"Path to the configuration directory" default:"~/.config/mctl"`

	// Force indicates whether to force reinitialization
	Force bool `name:"force" help:"Force reinitialization even if configuration already exists"`
}

// Run executes the init command
func (cmd *InitCmd) Run(ctx *kong.Context) error {
	// Create path handler for expanding paths
	pathHandler := lib.NewPathHandler()

	// Expand the config directory path
	expandedConfigDir, err := pathHandler.ExpandHomeDir(cmd.ConfigDir)
	if err != nil {
		return fmt.Errorf("failed to expand config directory path: %w", err)
	}

	// Check if configuration already exists
	configFilePath := filepath.Join(expandedConfigDir, "config.toml")
	if _, err := os.Stat(configFilePath); err == nil && !cmd.Force {
		return fmt.Errorf("configuration already exists at %s. Use --force to overwrite", configFilePath)
	}

	// Create prerequisites service and check prerequisites
	prereqService, err := services.NewPrerequisitesService()
	if err != nil {
		return fmt.Errorf("failed to create prerequisites service: %w", err)
	}

	fmt.Println("Checking prerequisites...")
	prerequisites, err := prereqService.CheckPrerequisites()
	if err != nil {
		return fmt.Errorf("failed to check prerequisites: %w", err)
	}

	// Display prerequisite check results
	if prerequisites.DockerInstalled {
		fmt.Println("✓ Docker installed")
	} else {
		fmt.Println("✗ Docker not installed")
		return fmt.Errorf("Docker is required but not installed. Please install Docker and try again")
	}

	if prerequisites.DockerRunning {
		fmt.Println("✓ Docker daemon running")
	} else {
		fmt.Println("✗ Docker daemon not running")
		return fmt.Errorf("Docker daemon is not running. Please start Docker and try again")
	}

	if prerequisites.DockerComposeAvailable {
		fmt.Println("✓ Docker Compose available")
	} else {
		fmt.Println("⚠ Docker Compose not available (optional but recommended)")
	}

	// Create configuration service
	configService := services.NewConfigService()

	// Generate default configuration
	fmt.Println("Creating default configuration...")
	config := configService.CreateDefaultConfig()

	// Ensure the configuration directory exists
	if err := os.MkdirAll(expandedConfigDir, 0755); err != nil {
		return fmt.Errorf("failed to create configuration directory %s: %w", expandedConfigDir, err)
	}

	// Save configuration to file
	if err := configService.SaveConfig(config, configFilePath); err != nil {
		return fmt.Errorf("failed to save configuration: %w", err)
	}

	fmt.Printf("✓ Configuration created at %s\n", configFilePath)
	fmt.Println("ARR-stack initialized successfully")

	return nil
}
