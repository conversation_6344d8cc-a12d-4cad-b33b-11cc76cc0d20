package cli

import (
	"context"
	"fmt"
	"os"
	"path/filepath"
	"strings"

	"github.com/alecthomas/kong"
	"github.com/idriesalbender/media-center-v3/src/lib"
	"github.com/idriesalbender/media-center-v3/src/models"
	"github.com/idriesalbender/media-center-v3/src/services"
)

// StartCmd represents the start command
type StartCmd struct {
	// Applications is a comma-separated list of applications to start
	Applications string `name:"applications" help:"Comma-separated list of applications to start"`
}

// Run executes the start command
func (cmd *StartCmd) Run(ctx *kong.Context) error {
	// Create path handler for expanding paths
	pathHandler := lib.NewPathHandler()
	
	// Determine the config directory
	configDir, err := pathHandler.ExpandHomeDir("~/.config/mctl")
	if err != nil {
		return fmt.Errorf("failed to expand config directory path: %w", err)
	}
	
	// Load configuration from file
	configFilePath := filepath.Join(configDir, "config.toml")
	if _, err := os.Stat(configFilePath); os.IsNotExist(err) {
		return fmt.Errorf("configuration file not found at %s. Please run 'mctl init' first", configFilePath)
	}
	
	// Create configuration service
	configService := services.NewConfigService()
	
	// Load the configuration
	config, err := configService.LoadConfig(configFilePath)
	if err != nil {
		return fmt.Errorf("failed to load configuration: %w", err)
	}
	
	// Create Docker service
	dockerService, err := services.NewDockerService()
	if err != nil {
		return fmt.Errorf("failed to create Docker service: %w", err)
	}
	
	// Check Docker connectivity
	if err := dockerService.ConnectToDockerDaemon(context.Background()); err != nil {
		return fmt.Errorf("failed to connect to Docker daemon: %w", err)
	}
	
	// Filter applications if specific ones are specified
	appConfigs := config.Applications
	if cmd.Applications != "" {
		appNames := strings.Split(cmd.Applications, ",")
		var filteredApps []models.ApplicationConfig
		
		// Create a map for quick lookup
		appNameMap := make(map[string]bool)
		for _, name := range appNames {
			appNameMap[strings.TrimSpace(name)] = true
		}
		
		// Filter applications
		for _, app := range config.Applications {
			if appNameMap[app.Name] {
				filteredApps = append(filteredApps, app)
			}
		}
		
		appConfigs = filteredApps
	}
	
	// Resolve dependencies to determine startup order
	appStateService := services.NewAppStateService()
	orderedApps, err := appStateService.ResolveDependencies(appConfigs)
	if err != nil {
		return fmt.Errorf("failed to resolve dependencies: %w", err)
	}
	
	fmt.Println("Starting applications in dependency order...")
	
	// Start each application in the correct order
	var appStates []*models.ApplicationState
	for _, appConfig := range orderedApps {
		if !appConfig.Enabled {
			continue
		}
		
		fmt.Printf("Starting %s...\n", appConfig.Name)
		
		// Start the application
		appState, err := dockerService.StartApplication(context.Background(), &appConfig)
		if err != nil {
			fmt.Printf("Failed to start %s: %v\n", appConfig.Name, err)
			// Continue with other applications
			continue
		}
		
		appStates = append(appStates, appState)
		fmt.Printf("✓ %s: started (container: %s)\n", appConfig.Name, appState.ContainerID)
	}
	
	fmt.Println("ARR-stack started successfully")
	
	return nil
}