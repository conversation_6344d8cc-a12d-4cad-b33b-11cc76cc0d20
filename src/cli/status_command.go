package cli

import (
	"context"
	"encoding/json"
	"fmt"
	"os"
	"path/filepath"
	"strings"
	"text/tabwriter"
	"time"

	"github.com/alecthomas/kong"
	"github.com/idriesalbender/media-center-v3/src/lib"
	"github.com/idriesalbender/media-center-v3/src/models"
	"github.com/idriesalbender/media-center-v3/src/services"
)

// StatusCmd represents the status command
type StatusCmd struct {
	// Format specifies the output format (table or json)
	Format string `name:"format" help:"Output format (table or json)" default:"table" enum:"table,json"`

	// Applications is a comma-separated list of applications to check status for
	Applications string `name:"applications" help:"Comma-separated list of applications to check status for"`
}

// Run executes the status command
func (cmd *StatusCmd) Run(ctx *kong.Context) error {
	// Create path handler for expanding paths
	pathHandler := lib.NewPathHandler()

	// Determine the config directory
	configDir, err := pathHandler.ExpandHomeDir("~/.config/mctl")
	if err != nil {
		return fmt.Errorf("failed to expand config directory path: %w", err)
	}

	// Load configuration from file
	configFilePath := filepath.Join(configDir, "config.toml")
	if _, err := os.Stat(configFilePath); os.IsNotExist(err) {
		return fmt.Errorf("configuration file not found at %s. Please run 'mctl init' first", configFilePath)
	}

	// Create configuration service
	configService := services.NewConfigService()

	// Load the configuration
	config, err := configService.LoadConfig(configFilePath)
	if err != nil {
		return fmt.Errorf("failed to load configuration: %w", err)
	}

	// Create Docker service
	dockerService, err := services.NewDockerService()
	if err != nil {
		return fmt.Errorf("failed to create Docker service: %w", err)
	}

	// Check Docker connectivity
	if err := dockerService.ConnectToDockerDaemon(context.Background()); err != nil {
		return fmt.Errorf("failed to connect to Docker daemon: %w", err)
	}

	// Get application states
	var appStates []*models.ApplicationState

	// If specific applications are specified, get their states
	if cmd.Applications != "" {
		appNames := strings.Split(cmd.Applications, ",")
		for _, appName := range appNames {
			appName = strings.TrimSpace(appName)
			// Get the current state of each specified application
			appState, err := dockerService.GetApplicationStatus(context.Background(), appName)
			if err != nil {
				fmt.Printf("Warning: failed to get status for %s: %v\n", appName, err)
				// Create a default stopped state
				now := time.Now()
				appState = &models.ApplicationState{
					Name:         appName,
					Status:       "error",
					ContainerID:  "",
					LastUpdated:  now,
					ErrorMessage: err.Error(),
				}
			}
			appStates = append(appStates, appState)
		}
	} else {
		// Get states for all applications in the configuration
		for _, appConfig := range config.Applications {
			appState, err := dockerService.GetApplicationStatus(context.Background(), appConfig.Name)
			if err != nil {
				fmt.Printf("Warning: failed to get status for %s: %v\n", appConfig.Name, err)
				// Create a default stopped state
				now := time.Now()
				appState = &models.ApplicationState{
					Name:         appConfig.Name,
					Status:       "error",
					ContainerID:  "",
					LastUpdated:  now,
					ErrorMessage: err.Error(),
				}
			}
			appStates = append(appStates, appState)
		}
	}

	// Output the status information in the requested format
	if cmd.Format == "json" {
		return cmd.outputJSON(appStates)
	}

	return cmd.outputTable(appStates)
}

// outputJSON outputs the application states in JSON format
func (cmd *StatusCmd) outputJSON(appStates []*models.ApplicationState) error {
	type StatusResponse struct {
		Status       string                     `json:"status"`
		Applications []*models.ApplicationState `json:"applications"`
	}

	response := StatusResponse{
		Status:       "success",
		Applications: appStates,
	}

	jsonData, err := json.MarshalIndent(response, "", "  ")
	if err != nil {
		return fmt.Errorf("failed to marshal status to JSON: %w", err)
	}

	fmt.Println(string(jsonData))
	return nil
}

// outputTable outputs the application states in table format
func (cmd *StatusCmd) outputTable(appStates []*models.ApplicationState) error {
	// Create a new tab writer
	w := tabwriter.NewWriter(os.Stdout, 0, 0, 3, ' ', 0)

	// Write the header
	fmt.Fprintln(w, "Application\tStatus\tContainer ID\tHealth")
	fmt.Fprintln(w, "-----------\t------\t------------\t------")

	// Write each application status
	for _, appState := range appStates {
		containerID := "-"
		if appState.ContainerID != "" {
			// Truncate container ID for display
			if len(appState.ContainerID) > 12 {
				containerID = appState.ContainerID[:12]
			} else {
				containerID = appState.ContainerID
			}
		}

		health := "-"
		if appState.Status == "running" {
			health = "healthy" // Simplified health check
		}

		fmt.Fprintf(w, "%s\t%s\t%s\t%s\n", appState.Name, appState.Status, containerID, health)
	}

	// Flush the writer
	return w.Flush()
}
