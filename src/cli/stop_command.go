package cli

import (
	"context"
	"fmt"
	"os"
	"path/filepath"
	"strings"

	"github.com/alecthomas/kong"
	"github.com/idriesalbender/media-center-v3/src/lib"
	"github.com/idriesalbender/media-center-v3/src/models"
	"github.com/idriesalbender/media-center-v3/src/services"
)

// StopCmd represents the stop command
type StopCmd struct {
	// Applications is a comma-separated list of applications to stop
	Applications string `name:"applications" help:"Comma-separated list of applications to stop"`
}

// Run executes the stop command
func (cmd *StopCmd) Run(ctx *kong.Context) error {
	// Create path handler for expanding paths
	pathHandler := lib.NewPathHandler()
	
	// Determine the config directory
	configDir, err := pathHandler.ExpandHomeDir("~/.config/mctl")
	if err != nil {
		return fmt.Errorf("failed to expand config directory path: %w", err)
	}
	
	// Load configuration from file
	configFilePath := filepath.Join(configDir, "config.toml")
	if _, err := os.Stat(configFilePath); os.IsNotExist(err) {
		return fmt.Errorf("configuration file not found at %s. Please run 'mctl init' first", configFilePath)
	}
	
	// Create configuration service
	configService := services.NewConfigService()
	
	// Load the configuration
	config, err := configService.LoadConfig(configFilePath)
	if err != nil {
		return fmt.Errorf("failed to load configuration: %w", err)
	}
	
	// Create Docker service
	dockerService, err := services.NewDockerService()
	if err != nil {
		return fmt.Errorf("failed to create Docker service: %w", err)
	}
	
	// Check Docker connectivity
	if err := dockerService.ConnectToDockerDaemon(context.Background()); err != nil {
		return fmt.Errorf("failed to connect to Docker daemon: %w", err)
	}
	
	// Get current application states
	var appStates []*models.ApplicationState
	
	// If specific applications are specified, get their states
	if cmd.Applications != "" {
		appNames := strings.Split(cmd.Applications, ",")
		for _, appName := range appNames {
			appName = strings.TrimSpace(appName)
			// Get the current state of each specified application
			appState, err := dockerService.GetApplicationStatus(context.Background(), appName)
			if err != nil {
				fmt.Printf("Warning: failed to get status for %s: %v\n", appName, err)
				continue
			}
			appStates = append(appStates, appState)
		}
	} else {
		// Get states for all applications in the configuration
		for _, appConfig := range config.Applications {
			appState, err := dockerService.GetApplicationStatus(context.Background(), appConfig.Name)
			if err != nil {
				fmt.Printf("Warning: failed to get status for %s: %v\n", appConfig.Name, err)
				continue
			}
			appStates = append(appStates, appState)
		}
	}
	
	// Stop the applications
	fmt.Println("Stopping applications...")
	
	for _, appState := range appStates {
		// Only stop applications that are running
		if appState.Status == "running" && appState.ContainerID != "" {
			fmt.Printf("Stopping %s...\n", appState.Name)
			
			// Stop the application
			if err := dockerService.StopApplication(context.Background(), appState); err != nil {
				fmt.Printf("Failed to stop %s: %v\n", appState.Name, err)
				// Continue with other applications
				continue
			}
			
			fmt.Printf("✓ %s: stopped\n", appState.Name)
		}
	}
	
	fmt.Println("ARR-stack stopped successfully")
	
	return nil
}