package lib

import (
	"fmt"
	"os"

	"github.com/idriesalbender/media-center-v3/src/models"
)

// ConfigParser handles parsing and serializing configuration files
type ConfigParser struct {
	pathHandler  *PathHandler
	tomlHandler   *TOMLHandler
}

// NewConfigParser creates a new ConfigParser instance
func NewConfigParser() *ConfigParser {
	return &ConfigParser{
		pathHandler: NewPathHandler(),
		tomlHandler: NewTOMLHandler(),
	}
}

// ParseConfig parses a TOML configuration file
func (p *ConfigParser) ParseConfig(filePath string) (*models.ARRStackConfiguration, error) {
	// Expand home directory if needed
	expandedPath, err := p.pathHandler.ExpandHomeDir(filePath)
	if err != nil {
		return nil, fmt.Errorf("failed to expand path %s: %w", filePath, err)
	}

	// Read the TOML file
	data, err := os.ReadFile(expandedPath)
	if err != nil {
		return nil, fmt.Errorf("failed to read configuration file %s: %w", expandedPath, err)
	}

	// Parse the TOML content into an ARRStackConfiguration struct
	var config models.ARRStackConfiguration
	if err := p.tomlHandler.Unmarshal(data, &config); err != nil {
		return nil, fmt.Errorf("failed to parse TOML configuration: %w", err)
	}

	// Validate the configuration
	if err := config.Validate(); err != nil {
		return nil, fmt.Errorf("invalid configuration: %w", err)
	}

	return &config, nil
}

// SerializeConfig serializes an ARRStackConfiguration to TOML format
func (p *ConfigParser) SerializeConfig(config *models.ARRStackConfiguration) ([]byte, error) {
	// Validate the configuration
	if err := config.Validate(); err != nil {
		return nil, fmt.Errorf("invalid configuration: %w", err)
	}

	// Convert the ARRStackConfiguration struct to TOML format
	data, err := p.tomlHandler.Marshal(config)
	if err != nil {
		return nil, fmt.Errorf("failed to serialize configuration to TOML: %w", err)
	}

	return data, nil
}

// WriteConfig writes an ARRStackConfiguration to a TOML file
func (p *ConfigParser) WriteConfig(config *models.ARRStackConfiguration, filePath string) error {
	// Expand home directory if needed
	expandedPath, err := p.pathHandler.ExpandHomeDir(filePath)
	if err != nil {
		return fmt.Errorf("failed to expand path %s: %w", filePath, err)
	}

	// Ensure the directory exists
	dir := expandedPath[:len(expandedPath)-len("/config.toml")] // Remove filename
	if err := os.MkdirAll(dir, 0755); err != nil {
		return fmt.Errorf("failed to create directory %s: %w", dir, err)
	}

	// Serialize the configuration to TOML format
	data, err := p.SerializeConfig(config)
	if err != nil {
		return fmt.Errorf("failed to serialize configuration: %w", err)
	}

	// Write the TOML content to the specified file path
	if err := os.WriteFile(expandedPath, data, 0644); err != nil {
		return fmt.Errorf("failed to write configuration file %s: %w", expandedPath, err)
	}

	return nil
}