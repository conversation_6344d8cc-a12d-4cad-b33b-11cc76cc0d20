package lib

import (
	"fmt"
)

// AppError represents a structured application error
type AppError struct {
	Code    string
	Message string
	Details string
}

// Error returns the error message
func (e *AppError) Error() string {
	if e.Details != "" {
		return fmt.Sprintf("%s: %s (%s)", e.Code, e.Message, e.Details)
	}
	return fmt.Sprintf("%s: %s", e.Code, e.Message)
}

// NewAppError creates a new AppError
func NewAppError(code, message, details string) *AppError {
	return &AppError{
		Code:    code,
		Message: message,
		Details: details,
	}
}

// Error codes
const (
	ErrCodeConfigInvalid     = "CONFIG_INVALID"
	ErrCodeDockerUnavailable = "DOCKER_UNAVAILABLE"
	ErrCodeContainerFailed   = "CONTAINER_FAILED"
	ErrCodePrerequisites     = "PREREQUISITES_MISSING"
	ErrCodePermissions       = "PERMISSIONS_DENIED"
	ErrCodeIOError           = "IO_ERROR"
	ErrCodeNetworkError      = "NETWORK_ERROR"
)

// UserFriendlyMessage returns a user-friendly error message
func UserFriendlyMessage(err error) string {
	// If it's our custom AppError, provide a user-friendly message
	if appErr, ok := err.(*AppError); ok {
		switch appErr.Code {
		case ErrCodeConfigInvalid:
			return fmt.Sprintf("Configuration error: %s. Please check your configuration file.", appErr.Message)
		case ErrCodeDockerUnavailable:
			return "Docker is not available. Please ensure Docker is installed and running."
		case ErrCodeContainerFailed:
			return fmt.Sprintf("Failed to manage container: %s. Please check the application logs for details.", appErr.Message)
		case ErrCodePrerequisites:
			return fmt.Sprintf("Missing prerequisites: %s. Please install the required components.", appErr.Message)
		case ErrCodePermissions:
			return "Permission denied. Please check that you have the necessary permissions to perform this operation."
		case ErrCodeIOError:
			return fmt.Sprintf("File operation failed: %s. Please check that the file paths are correct and accessible.", appErr.Message)
		case ErrCodeNetworkError:
			return fmt.Sprintf("Network error: %s. Please check your network connection and try again.", appErr.Message)
		default:
			return fmt.Sprintf("An error occurred: %s. Please check the application logs for details.", appErr.Message)
		}
	}
	
	// For other errors, provide a generic message
	return fmt.Sprintf("An unexpected error occurred: %s. Please check the application logs for details.", err.Error())
}