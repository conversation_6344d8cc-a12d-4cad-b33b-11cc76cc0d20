package lib

import (
	"fmt"
	"log"
	"net/http"
	"os"
	"time"
)

// LogLevel represents the severity level of a log message
type LogLevel int

const (
	DEBUG LogLevel = iota
	INFO
	WARN
	ERROR
)

// String returns the string representation of a LogLevel
func (l LogLevel) String() string {
	switch l {
	case DEBUG:
		return "DEBUG"
	case INFO:
		return "INFO"
	case WARN:
		return "WARN"
	case ERROR:
		return "ERROR"
	default:
		return "UNKNOWN"
	}
}

// Logger provides structured logging capabilities
type Logger struct {
	level  LogLevel
	logger *log.Logger
}

// NewLogger creates a new Logger instance
func NewLogger(level LogLevel, logFile string) (*Logger, error) {
	var writer *os.File
	var err error
	
	if logFile != "" {
		writer, err = os.OpenFile(logFile, os.O_CREATE|os.O_WRONLY|os.O_APPEND, 0644)
		if err != nil {
			return nil, fmt.Errorf("failed to open log file: %w", err)
		}
	} else {
		writer = os.Stdout
	}
	
	return &Logger{
		level:  level,
		logger: log.New(writer, "", log.LstdFlags),
	}, nil
}

// Log writes a log message at the specified level
func (l *Logger) Log(level LogLevel, format string, args ...interface{}) {
	if level >= l.level {
		message := fmt.Sprintf(format, args...)
		l.logger.Printf("[%s] %s", level.String(), message)
	}
}

// Debug logs a debug message
func (l *Logger) Debug(format string, args ...interface{}) {
	l.Log(DEBUG, format, args...)
}

// Info logs an info message
func (l *Logger) Info(format string, args ...interface{}) {
	l.Log(INFO, format, args...)
}

// Warn logs a warning message
func (l *Logger) Warn(format string, args ...interface{}) {
	l.Log(WARN, format, args...)
}

// Error logs an error message
func (l *Logger) Error(format string, args ...interface{}) {
	l.Log(ERROR, format, args...)
}

// HealthChecker provides health checking capabilities
type HealthChecker struct {
	logger *Logger
}

// NewHealthChecker creates a new HealthChecker instance
func NewHealthChecker(logger *Logger) *HealthChecker {
	return &HealthChecker{
		logger: logger,
	}
}

// CheckHealth performs a health check
func (h *HealthChecker) CheckHealth(component string) error {
	// For now, we'll just log that we're performing a health check
	// A more complete implementation would actually check the component's health
	h.logger.Debug("Performing health check on %s", component)
	return nil
}

// CheckHTTPHealth performs an HTTP-based health check
func (h *HealthChecker) CheckHTTPHealth(url string, timeout time.Duration) error {
	// Create an HTTP client with the specified timeout
	client := &http.Client{
		Timeout: timeout,
	}
	
	// Make an HTTP request to the specified URL
	h.logger.Debug("Performing HTTP health check on %s with timeout %v", url, timeout)
	
	resp, err := client.Get(url)
	if err != nil {
		return fmt.Errorf("HTTP health check failed for %s: %w", url, err)
	}
	defer resp.Body.Close()
	
	// Check if the response status is healthy (2xx)
	if resp.StatusCode < 200 || resp.StatusCode >= 300 {
		return fmt.Errorf("HTTP health check failed for %s: status code %d", url, resp.StatusCode)
	}
	
	h.logger.Debug("HTTP health check succeeded for %s: status code %d", url, resp.StatusCode)
	return nil
}