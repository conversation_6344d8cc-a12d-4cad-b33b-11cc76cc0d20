package lib

import (
	"fmt"
	"os"
	"path/filepath"
	"runtime"
)

// PathHandler provides cross-platform path handling capabilities
type PathHandler struct {
	// No fields needed for this implementation
}

// NewPathHandler creates a new PathHandler instance
func NewPathHandler() *PathHandler {
	return &PathHandler{}
}

// ExpandHomeDir expands the ~ symbol to the user's home directory
func (p *PathHandler) ExpandHomeDir(path string) (string, error) {
	if len(path) == 0 || path[0] != '~' {
		return path, nil
	}
	
	homeDir, err := os.UserHomeDir()
	if err != nil {
		return "", fmt.Errorf("failed to get user home directory: %w", err)
	}
	
	if len(path) > 1 && path[1] != filepath.Separator {
		return "", fmt.Errorf("invalid path format: %s", path)
	}
	
	if len(path) == 1 {
		return homeDir, nil
	}
	
	return filepath.Join(homeDir, path[2:]), nil
}

// NormalizePath converts a path to the standard format for the current platform
func (p *PathHandler) NormalizePath(path string) string {
	return filepath.Clean(path)
}

// IsAbsolutePath checks if a path is absolute
func (p *PathHandler) IsAbsolutePath(path string) bool {
	return filepath.IsAbs(path)
}

// JoinPath joins multiple path elements into a single path
func (p *PathHandler) JoinPath(elements ...string) string {
	return filepath.Join(elements...)
}

// GetConfigDir returns the default configuration directory for the application
func (p *PathHandler) GetConfigDir() (string, error) {
	homeDir, err := os.UserHomeDir()
	if err != nil {
		return "", fmt.Errorf("failed to get user home directory: %w", err)
	}
	
	// Use different config directories based on the platform
	switch runtime.GOOS {
	case "windows":
		return filepath.Join(homeDir, "AppData", "Local", "mctl"), nil
	default:
		return filepath.Join(homeDir, ".config", "mctl"), nil
	}
}

// GetDataDir returns the default data directory for the application
func (p *PathHandler) GetDataDir() (string, error) {
	homeDir, err := os.UserHomeDir()
	if err != nil {
		return "", fmt.Errorf("failed to get user home directory: %w", err)
	}
	
	// Use different data directories based on the platform
	switch runtime.GOOS {
	case "windows":
		return filepath.Join(homeDir, "AppData", "Local", "mctl", "data"), nil
	default:
		return filepath.Join(homeDir, ".local", "share", "mctl"), nil
	}
}

// EnsureDirExists creates a directory if it doesn't exist
func (p *PathHandler) EnsureDirExists(path string) error {
	// Expand home directory if needed
	expandedPath, err := p.ExpandHomeDir(path)
	if err != nil {
		return err
	}
	
	// Create the directory with all parent directories
	err = os.MkdirAll(expandedPath, 0755)
	if err != nil {
		return fmt.Errorf("failed to create directory %s: %w", expandedPath, err)
	}
	
	return nil
}