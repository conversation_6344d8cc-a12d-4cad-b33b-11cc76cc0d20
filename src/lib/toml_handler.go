package lib

import (
	"bytes"
	"fmt"

	"github.com/BurntSushi/toml"
)

// TOMLHandler handles TOML serialization and deserialization
type TOMLHandler struct {
	// No fields needed for this implementation
}

// NewTOMLHandler creates a new TOMLHandler instance
func NewTOMLHandler() *TOMLHandler {
	return &TOMLHandler{}
}

// Marshal converts a struct to TOML bytes
func (h *TOMLHandler) Marshal(v interface{}) ([]byte, error) {
	// Convert the provided struct to TOML format
	var buf bytes.Buffer
	encoder := toml.NewEncoder(&buf)
	
	if err := encoder.Encode(v); err != nil {
		return nil, fmt.Errorf("failed to marshal to TOML: %w", err)
	}
	
	return buf.Bytes(), nil
}

// Unmarshal converts TOML bytes to a struct
func (h *TOMLHandler) Unmarshal(data []byte, v interface{}) error {
	// Parse the TOML bytes
	if err := toml.Unmarshal(data, v); err != nil {
		return fmt.Errorf("failed to unmarshal from TOML: %w", err)
	}
	
	return nil
}

// ValidateTOML checks if TOML data is valid
func (h *TOMLHandler) ValidateTOML(data []byte) error {
	// Parse the TOML data to check for syntax errors
	var temp interface{}
	if err := toml.Unmarshal(data, &temp); err != nil {
		return fmt.Errorf("invalid TOML syntax: %w", err)
	}
	
	return nil
}