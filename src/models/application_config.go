package models

import (
	"fmt"
)

// ApplicationConfig represents the configuration for a single application in the stack.
type ApplicationConfig struct {
	// Name is the unique identifier for the application
	Name string `toml:"name"`
	
	// Type is the type of application (radarr, sonarr, prowlarr, etc.)
	Type string `toml:"type"`
	
	// Enabled indicates whether the application should be managed
	Enabled bool `toml:"enabled"`
	
	// Image is the Docker image to use
	Image string `toml:"image"`
	
	// Version is the version/tag of the Docker image
	Version string `toml:"version"`
	
	// Ports is a list of port mappings for the container
	Ports []PortMapping `toml:"ports"`
	
	// Volumes is a list of volume mappings for the container
	Volumes []VolumeMapping `toml:"volumes"`
	
	// Environment is a map of environment variables
	Environment map[string]string `toml:"environment"`
	
	// DependsOn is a list of names of applications this one depends on
	DependsOn []string `toml:"depends_on"`
	
	// HealthCheck contains health check configuration
	HealthCheck HealthCheckConfig `toml:"health_check"`
}

// Validate checks if the application configuration is valid
func (c *ApplicationConfig) Validate() error {
	// Name must not be empty
	if c.Name == "" {
		return fmt.Errorf("application name cannot be empty")
	}
	
	// Type must not be empty
	if c.Type == "" {
		return fmt.Errorf("application type cannot be empty")
	}
	
	// Image must not be empty
	if c.Image == "" {
		return fmt.Errorf("docker image cannot be empty")
	}
	
	// Validate port mappings
	for i, port := range c.Ports {
		if err := port.Validate(); err != nil {
			return fmt.Errorf("invalid port mapping at index %d: %w", i, err)
		}
	}
	
	// Validate volume mappings
	for i, volume := range c.Volumes {
		if err := volume.Validate(); err != nil {
			return fmt.Errorf("invalid volume mapping at index %d: %w", i, err)
		}
	}
	
	// Validate health check configuration
	if err := c.HealthCheck.Validate(); err != nil {
		return fmt.Errorf("invalid health check configuration: %w", err)
	}
	
	return nil
}