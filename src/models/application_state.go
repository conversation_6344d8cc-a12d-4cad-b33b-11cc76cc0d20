package models

import (
	"fmt"
	"regexp"
	"time"
)

// ApplicationState represents the current running state of an application.
type ApplicationState struct {
	// Name is the name of the application
	Name string `json:"name"`
	
	// Status is the current status (running, stopped, error, starting, stopping)
	Status string `json:"status"`
	
	// ContainerID is the Docker container ID (if applicable)
	ContainerID string `json:"container_id"`
	
	// LastUpdated is when the status was last updated
	LastUpdated time.Time `json:"last_updated"`
	
	// ErrorMessage is the error message if in error state
	ErrorMessage string `json:"error_message"`
}

// Validate checks if the application state is valid
func (a *ApplicationState) Validate() error {
	// Name must not be empty
	if a.Name == "" {
		return fmt.Errorf("application name cannot be empty")
	}
	
	// Status must be one of the defined status values
	validStatuses := map[string]bool{
		"running":  true,
		"stopped":  true,
		"error":    true,
		"starting": true,
		"stopping": true,
	}
	
	if !validStatuses[a.Status] {
		return fmt.Errorf("status must be one of running, stopped, error, starting, stopping, got %q", a.Status)
	}
	
	// Container ID must be a valid Docker container ID format if present
	if a.ContainerID != "" {
		// Docker container IDs are typically hex strings of 64 characters or short 12-character IDs
		containerIDRegex := regexp.MustCompile(`^[a-f0-9]{64}$`)
		shortIDRegex := regexp.MustCompile(`^[a-f0-9]{12}$`)
		
		if !containerIDRegex.MatchString(a.ContainerID) && !shortIDRegex.MatchString(a.ContainerID) {
			return fmt.Errorf("container ID must be a valid Docker container ID, got %q", a.ContainerID)
		}
	}
	
	// Last updated must be a valid timestamp (not zero time)
	if a.LastUpdated.IsZero() {
		return fmt.Errorf("last updated timestamp cannot be zero")
	}
	
	return nil
}