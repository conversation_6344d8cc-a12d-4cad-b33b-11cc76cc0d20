package models

import (
	"fmt"
	"regexp"
)

// ARRStackConfiguration represents the code-based configuration for the entire ARR-stack in TOML format.
type ARRStackConfiguration struct {
	// Version is the configuration schema version
	Version string `toml:"version"`
	
	// Applications is a list of configured applications
	Applications []ApplicationConfig `toml:"applications"`
	
	// GlobalSettings contains global configuration settings
	GlobalSettings GlobalSettings `toml:"global"`
	
	// Observability contains observability configuration
	Observability ObservabilityConfig `toml:"observability"`
}

// Validate checks if the configuration is valid
func (c *ARRStackConfiguration) Validate() error {
	// Version must be a valid semantic version
	semverRegex := regexp.MustCompile(`^\d+\.\d+\.\d+$`)
	if !semverRegex.MatchString(c.Version) {
		return fmt.Errorf("version must be a valid semantic version (X.Y.Z), got %q", c.Version)
	}
	
	// At least one application must be configured
	if len(c.Applications) == 0 {
		return fmt.Errorf("at least one application must be configured")
	}
	
	// Application names must be unique
	appNames := make(map[string]bool)
	for _, app := range c.Applications {
		if appNames[app.Name] {
			return fmt.Errorf("duplicate application name: %q", app.Name)
		}
		appNames[app.Name] = true
	}
	
	// Validate each application configuration
	for i, app := range c.Applications {
		if err := app.Validate(); err != nil {
			return fmt.Errorf("invalid application at index %d: %w", i, err)
		}
	}
	
	// Validate global settings
	if err := c.GlobalSettings.Validate(); err != nil {
		return fmt.Errorf("invalid global settings: %w", err)
	}
	
	// Validate observability configuration
	if err := c.Observability.Validate(); err != nil {
		return fmt.Errorf("invalid observability configuration: %w", err)
	}
	
	return nil
}