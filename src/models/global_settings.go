package models

import (
	"fmt"
	"path/filepath"
)

// GlobalSettings represents global configuration settings for the stack.
type GlobalSettings struct {
	// DataDirectory is the base directory for application data
	DataDirectory string `toml:"data_directory"`
	
	// ConfigDirectory is the base directory for configuration files
	ConfigDirectory string `toml:"config_directory"`
	
	// BackupDirectory is the directory for backups
	BackupDirectory string `toml:"backup_directory"`
	
	// Timezone is the timezone for applications
	Timezone string `toml:"timezone"`
}

// Validate checks if the global settings are valid
func (g *GlobalSettings) Validate() error {
	// All directory paths must be absolute
	if !filepath.IsAbs(g.DataDirectory) {
		return fmt.Errorf("data directory must be an absolute path, got %q", g.DataDirectory)
	}
	
	if !filepath.IsAbs(g.ConfigDirectory) {
		return fmt.Errorf("config directory must be an absolute path, got %q", g.ConfigDirectory)
	}
	
	if !filepath.IsAbs(g.BackupDirectory) {
		return fmt.Errorf("backup directory must be an absolute path, got %q", g.BackupDirectory)
	}
	
	// Timezone must be a valid tz database identifier
	// For now, we'll do a basic check - a more comprehensive check would
	// involve validating against the actual tz database
	if g.Timezone == "" {
		return fmt.Errorf("timezone cannot be empty")
	}
	
	// Basic format check - should contain at least one slash or be UTC
	if g.Timezone != "UTC" && g.Timezone != "GMT" && len(g.Timezone) < 3 {
		return fmt.Errorf("timezone should be a valid tz database identifier, got %q", g.Timezone)
	}
	
	return nil
}