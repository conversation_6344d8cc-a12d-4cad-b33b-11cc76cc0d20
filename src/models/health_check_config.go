package models

import (
	"fmt"
	"strings"
)

// HealthCheckConfig represents health check configuration for an application.
type HealthCheckConfig struct {
	// Endpoint is the HTTP endpoint to check
	Endpoint string `toml:"endpoint"`
	
	// Port is the port to check (if different from main port)
	Port int `toml:"port"`
	
	// Timeout is the timeout in seconds
	Timeout int `toml:"timeout"`
	
	// Retries is the number of retries before marking as failed
	Retries int `toml:"retries"`
}

// Validate checks if the health check configuration is valid
func (h *HealthCheckConfig) Validate() error {
	// Endpoint must be a valid HTTP path
	if h.Endpoint == "" {
		return fmt.Errorf("endpoint cannot be empty")
	}
	
	// Endpoint should start with /
	if !strings.HasPrefix(h.Endpoint, "/") {
		return fmt.Errorf("endpoint must start with '/', got %q", h.Endpoint)
	}
	
	// Port must be in valid range (1-65535)
	if h.Port < 0 || h.Port > 65535 {
		return fmt.Errorf("port must be in range 0-65535, got %d", h.Port)
	}
	
	// Timeout must be positive
	if h.Timeout <= 0 {
		return fmt.Errorf("timeout must be positive, got %d", h.Timeout)
	}
	
	// Retries must be non-negative
	if h.Retries < 0 {
		return fmt.Errorf("retries must be non-negative, got %d", h.Retries)
	}
	
	return nil
}