package models

import (
	"fmt"
	"path/filepath"
)

// ObservabilityConfig represents observability configuration for the stack.
type ObservabilityConfig struct {
	// LogLevel is the logging level (debug, info, warn, error)
	LogLevel string `toml:"log_level"`
	
	// LogDirectory is the directory for log files
	LogDirectory string `toml:"log_directory"`
	
	// MetricsEnabled indicates whether to collect metrics
	MetricsEnabled bool `toml:"metrics_enabled"`
	
	// HealthCheckInterval is the interval in seconds for health checks
	HealthCheckInterval int `toml:"health_check_interval"`
}

// Validate checks if the observability configuration is valid
func (o *ObservabilityConfig) Validate() error {
	// Log level must be one of the supported levels
	validLogLevels := map[string]bool{
		"debug": true,
		"info":  true,
		"warn":  true,
		"error": true,
	}
	
	if !validLogLevels[o.LogLevel] {
		return fmt.Errorf("log level must be one of debug, info, warn, error, got %q", o.LogLevel)
	}
	
	// Log directory must be an absolute path
	if !filepath.IsAbs(o.LogDirectory) {
		return fmt.Errorf("log directory must be an absolute path, got %q", o.LogDirectory)
	}
	
	// Health check interval must be positive
	if o.HealthCheckInterval <= 0 {
		return fmt.Errorf("health check interval must be positive, got %d", o.HealthCheckInterval)
	}
	
	return nil
}