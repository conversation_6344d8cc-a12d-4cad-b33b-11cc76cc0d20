package models

import (
	"fmt"
)

// PortMapping represents a mapping between host and container ports.
type PortMapping struct {
	// HostPort is the port on the host system
	HostPort int `toml:"host_port"`
	
	// ContainerPort is the port inside the container
	ContainerPort int `toml:"container_port"`
	
	// Protocol is the network protocol (tcp, udp)
	Protocol string `toml:"protocol"`
}

// Validate checks if the port mapping is valid
func (p *PortMapping) Validate() error {
	// Host port must be in valid range (1-65535)
	if p.HostPort < 1 || p.HostPort > 65535 {
		return fmt.Errorf("host port must be in range 1-65535, got %d", p.HostPort)
	}
	
	// Container port must be in valid range (1-65535)
	if p.ContainerPort < 1 || p.ContainerPort > 65535 {
		return fmt.Errorf("container port must be in range 1-65535, got %d", p.ContainerPort)
	}
	
	// Protocol must be either \"tcp\" or \"udp\"
	if p.Protocol != \"tcp\" && p.Protocol != \"udp\" {
		return fmt.Errorf(\"protocol must be either \\\"tcp\\\" or \\\"udp\\\", got %q\", p.Protocol)
	}
	
	return nil
}