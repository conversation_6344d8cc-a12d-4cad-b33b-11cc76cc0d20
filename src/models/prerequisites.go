package models

import (
	"fmt"
)

// Prerequisites represents the system prerequisites needed to run the ARR-stack.
type Prerequisites struct {
	// DockerInstalled indicates whether <PERSON><PERSON> is installed
	DockerInstalled bool `json:"docker_installed"`
	
	// DockerRunning indicates whether <PERSON><PERSON> daemon is running
	DockerRunning bool `json:"docker_running"`
	
	// DockerComposeAvailable indicates whether Docker Compose is available
	DockerComposeAvailable bool `json:"docker_compose_available"`
	
	// SystemResources contains available system resources
	SystemResources SystemResources `json:"system_resources"`
}

// Validate checks if the prerequisites are valid
func (p *Prerequisites) Validate() error {
	// Validate system resources
	if err := p.SystemResources.Validate(); err != nil {
		return fmt.Errorf("invalid system resources: %w", err)
	}
	
	return nil
}