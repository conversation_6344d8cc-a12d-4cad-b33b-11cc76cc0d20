package models

import (
	"fmt"
)

// SystemResources represents available system resources.
type SystemResources struct {
	// TotalMemoryMB is the total system memory in MB
	TotalMemoryMB int `json:"total_memory_mb"`
	
	// AvailableMemoryMB is the available memory in MB
	AvailableMemoryMB int `json:"available_memory_mb"`
	
	// TotalDiskGB is the total disk space in GB
	TotalDiskGB int `json:"total_disk_gb"`
	
	// AvailableDiskGB is the available disk space in GB
	AvailableDiskGB int `json:"available_disk_gb"`
}

// Validate checks if the system resources are valid
func (s *SystemResources) Validate() error {
	// All resource values should be non-negative
	if s.TotalMemoryMB < 0 {
		return fmt.Errorf("total memory must be non-negative, got %d", s.TotalMemoryMB)
	}
	
	if s.AvailableMemoryMB < 0 {
		return fmt.Errorf("available memory must be non-negative, got %d", s.AvailableMemoryMB)
	}
	
	if s.TotalDiskGB < 0 {
		return fmt.Errorf("total disk space must be non-negative, got %d", s.TotalDiskGB)
	}
	
	if s.AvailableDiskGB < 0 {
		return fmt.Errorf("available disk space must be non-negative, got %d", s.AvailableDiskGB)
	}
	
	// Available resources should not exceed total resources
	if s.AvailableMemoryMB > s.TotalMemoryMB {
		return fmt.Errorf("available memory (%d MB) cannot exceed total memory (%d MB)", s.AvailableMemoryMB, s.TotalMemoryMB)
	}
	
	if s.AvailableDiskGB > s.TotalDiskGB {
		return fmt.Errorf("available disk space (%d GB) cannot exceed total disk space (%d GB)", s.AvailableDiskGB, s.TotalDiskGB)
	}
	
	return nil
}