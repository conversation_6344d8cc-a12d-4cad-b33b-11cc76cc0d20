package models

import (
	"fmt"
	"os"
	"path/filepath"
)

// VolumeMapping represents a mapping between host and container file system paths.
type VolumeMapping struct {
	// HostPath is the path on the host system
	HostPath string `toml:"host_path"`
	
	// ContainerPath is the path inside the container
	ContainerPath string `toml:"container_path"`
	
	// ReadOnly indicates whether the volume should be read-only
	ReadOnly bool `toml:"read_only"`
}

// Validate checks if the volume mapping is valid
func (v *VolumeMapping) Validate() error {
	// Host path must be an absolute path
	if !filepath.IsAbs(v.HostPath) {
		return fmt.Errorf("host path must be an absolute path, got %q", v.HostPath)
	}
	
	// Container path must be an absolute path
	if !filepath.IsAbs(v.ContainerPath) {
		return fmt.Errorf("container path must be an absolute path, got %q", v.ContainerPath)
	}
	
	// Host path must exist or be creatable
	// We'll check if the parent directory exists and is writable
	dir := filepath.Dir(v.HostPath)
	if _, err := os.Stat(dir); os.IsNotExist(err) {
		return fmt.Errorf("parent directory of host path %q does not exist", dir)
	}
	
	// Check if we can write to the directory (if the file doesn't exist)
	if _, err := os.Stat(v.HostPath); os.IsNotExist(err) {
		// File doesn't exist, check if we can create it
		testFile := filepath.Join(dir, ".mctl_test")
		if err := os.WriteFile(testFile, []byte(""), 0644); err != nil {
			return fmt.Errorf("cannot create files in directory %q: %w", dir, err)
		}
		os.Remove(testFile) // Clean up test file
	}
	
	return nil
}