package services

import (
	"fmt"
	"sync"
	"time"

	"github.com/idriesalbender/media-center-v3/src/models"
)

// AppStateService manages the state of applications in the ARR-stack
type AppStateService struct {
	// In-memory storage for application states
	states map[string]*models.ApplicationState
	mu     sync.RWMutex
}

// NewAppStateService creates a new AppStateService instance
func NewAppStateService() *AppStateService {
	return &AppStateService{
		states: make(map[string]*models.ApplicationState),
	}
}

// GetApplicationState retrieves the current state of an application
func (s *AppStateService) GetApplicationState(appName string) (*models.ApplicationState, error) {
	s.mu.RLock()
	defer s.mu.RUnlock()

	state, exists := s.states[appName]
	if !exists {
		return nil, fmt.Errorf("application %s not found", appName)
	}

	// Return a copy to prevent external modification
	return &models.ApplicationState{
		Name:         state.Name,
		Status:       state.Status,
		ContainerID:  state.ContainerID,
		LastUpdated:  state.LastUpdated,
		ErrorMessage: state.ErrorMessage,
	}, nil
}

// UpdateApplicationState updates the state of an application
func (s *AppStateService) UpdateApplicationState(state *models.ApplicationState) error {
	if state == nil {
		return fmt.Errorf("state cannot be nil")
	}

	s.mu.Lock()
	defer s.mu.Unlock()

	// Validate the state
	if err := state.Validate(); err != nil {
		return fmt.Errorf("invalid state: %w", err)
	}

	// Store a copy to prevent external modification
	s.states[state.Name] = &models.ApplicationState{
		Name:         state.Name,
		Status:       state.Status,
		ContainerID:  state.ContainerID,
		LastUpdated:  state.LastUpdated,
		ErrorMessage: state.ErrorMessage,
	}

	return nil
}

// GetAllApplicationStates retrieves the current state of all applications
func (s *AppStateService) GetAllApplicationStates() ([]*models.ApplicationState, error) {
	s.mu.RLock()
	defer s.mu.RUnlock()

	states := make([]*models.ApplicationState, 0, len(s.states))
	for _, state := range s.states {
		// Return copies to prevent external modification
		states = append(states, &models.ApplicationState{
			Name:         state.Name,
			Status:       state.Status,
			ContainerID:  state.ContainerID,
			LastUpdated:  state.LastUpdated,
			ErrorMessage: state.ErrorMessage,
		})
	}

	return states, nil
}

// ResolveDependencies determines the correct order to start/stop applications based on dependencies
func (s *AppStateService) ResolveDependencies(appConfigs []models.ApplicationConfig) ([]models.ApplicationConfig, error) {
	// Create a map of application names to configs for easy lookup
	appMap := make(map[string]models.ApplicationConfig)
	for _, app := range appConfigs {
		appMap[app.Name] = app
	}
	
	// Track which applications we've visited and which are in the current path (for cycle detection)
	visited := make(map[string]bool)
	inPath := make(map[string]bool)
	
	// Result slice to hold applications in dependency order
	var result []models.ApplicationConfig
	
	// Helper function for topological sort using DFS
	var visit func(string) error
	visit = func(appName string) error {
		// If we've already visited this app, skip it
		if visited[appName] {
			return nil
		}
		
		// If this app is in our current path, we have a cycle
		if inPath[appName] {
			return fmt.Errorf("circular dependency detected involving %s", appName)
		}
		
		// Mark this app as in the current path
		inPath[appName] = true
		
		// Visit all dependencies first
		appConfig := appMap[appName]
		for _, depName := range appConfig.DependsOn {
			// Check if the dependency exists
			if _, exists := appMap[depName]; !exists {
				return fmt.Errorf("dependency %s for application %s not found", depName, appName)
			}
			
			// Recursively visit the dependency
			if err := visit(depName); err != nil {
				return err
			}
		}
		
		// Mark this app as visited and remove from current path
		visited[appName] = true
		inPath[appName] = false
		
		// Add this app to the result
		result = append(result, appConfig)
		
		return nil
	}
	
	// Visit each application
	for _, app := range appConfigs {
		if err := visit(app.Name); err != nil {
			return nil, err
		}
	}
	
	return result, nil
}

// SetApplicationState sets the state of an application (helper method)
func (s *AppStateService) SetApplicationState(appName, status, containerID, errorMessage string) error {
	state := &models.ApplicationState{
		Name:         appName,
		Status:       status,
		ContainerID:  containerID,
		LastUpdated:  time.Now(),
		ErrorMessage: errorMessage,
	}

	return s.UpdateApplicationState(state)
}

// RemoveApplicationState removes the state of an application
func (s *AppStateService) RemoveApplicationState(appName string) error {
	s.mu.Lock()
	defer s.mu.Unlock()

	if _, exists := s.states[appName]; !exists {
		return fmt.Errorf("application %s not found", appName)
	}

	delete(s.states, appName)
	return nil
}