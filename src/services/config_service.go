package services

import (
	"fmt"
	"os"
	"path/filepath"

	"github.com/BurntSushi/toml"
	"github.com/idriesalbender/media-center-v3/src/lib"
	"github.com/idriesalbender/media-center-v3/src/models"
)

// ConfigService handles configuration-related operations
type ConfigService struct {
	pathHandler *lib.PathHandler
}

// NewConfigService creates a new ConfigService instance
func NewConfigService() *ConfigService {
	return &ConfigService{
		pathHandler: lib.NewPathHandler(),
	}
}

// LoadConfig loads the ARR-stack configuration from a file
func (s *ConfigService) LoadConfig(filePath string) (*models.ARRStackConfiguration, error) {
	// Expand home directory if needed
	expandedPath, err := s.pathHandler.ExpandHomeDir(filePath)
	if err != nil {
		return nil, fmt.Errorf("failed to expand path %s: %w", filePath, err)
	}

	// Read the TOML file
	data, err := os.ReadFile(expandedPath)
	if err != nil {
		return nil, fmt.Errorf("failed to read configuration file %s: %w", expandedPath, err)
	}

	// Parse the TOML content into an ARRStackConfiguration struct
	var config models.ARRStackConfiguration
	if err := toml.Unmarshal(data, &config); err != nil {
		return nil, fmt.Errorf("failed to parse TOML configuration: %w", err)
	}

	// Validate the configuration
	if err := config.Validate(); err != nil {
		return nil, fmt.Errorf("invalid configuration: %w", err)
	}

	return &config, nil
}

// SaveConfig saves the ARR-stack configuration to a file
func (s *ConfigService) SaveConfig(config *models.ARRStackConfiguration, filePath string) error {
	// Validate the configuration
	if err := config.Validate(); err != nil {
		return fmt.Errorf("invalid configuration: %w", err)
	}

	// Expand home directory if needed
	expandedPath, err := s.pathHandler.ExpandHomeDir(filePath)
	if err != nil {
		return fmt.Errorf("failed to expand path %s: %w", filePath, err)
	}

	// Ensure the directory exists
	dir := filepath.Dir(expandedPath)
	if err := os.MkdirAll(dir, 0755); err != nil {
		return fmt.Errorf("failed to create directory %s: %w", dir, err)
	}

	// Serialize the configuration to TOML format
	data, err := toml.Marshal(config)
	if err != nil {
		return fmt.Errorf("failed to serialize configuration to TOML: %w", err)
	}

	// Write the TOML content to the specified file path
	if err := os.WriteFile(expandedPath, data, 0644); err != nil {
		return fmt.Errorf("failed to write configuration file %s: %w", expandedPath, err)
	}

	return nil
}

// CreateDefaultConfig creates a default ARR-stack configuration
func (s *ConfigService) CreateDefaultConfig() *models.ARRStackConfiguration {
	return &models.ARRStackConfiguration{
		Version: "1.0.0",
		Applications: []models.ApplicationConfig{
			{
				Name:    "radarr",
				Type:    "radarr",
				Enabled: true,
				Image:   "lscr.io/linuxserver/radarr",
				Version: "latest",
				Ports: []models.PortMapping{
					{
						HostPort:      7878,
						ContainerPort: 7878,
						Protocol:      "tcp",
					},
				},
				Volumes: []models.VolumeMapping{
					{
						HostPath:      "~/media/movies",
						ContainerPath: "/movies",
						ReadOnly:      false,
					},
					{
						HostPath:      "~/.config/mctl/apps/radarr",
						ContainerPath: "/config",
						ReadOnly:      false,
					},
				},
				Environment: map[string]string{
					"PUID": "1000",
					"PGID": "1000",
					"TZ":   "UTC",
				},
				DependsOn: []string{},
				HealthCheck: models.HealthCheckConfig{
					Endpoint: "/api/v3/health",
					Port:     7878,
					Timeout:  10,
					Retries:  3,
				},
			},
			{
				Name:    "sonarr",
				Type:    "sonarr",
				Enabled: true,
				Image:   "lscr.io/linuxserver/sonarr",
				Version: "latest",
				Ports: []models.PortMapping{
					{
						HostPort:      8989,
						ContainerPort: 8989,
						Protocol:      "tcp",
					},
				},
				Volumes: []models.VolumeMapping{
					{
						HostPath:      "~/media/tv",
						ContainerPath: "/tv",
						ReadOnly:      false,
					},
					{
						HostPath:      "~/.config/mctl/apps/sonarr",
						ContainerPath: "/config",
						ReadOnly:      false,
					},
				},
				Environment: map[string]string{
					"PUID": "1000",
					"PGID": "1000",
					"TZ":   "UTC",
				},
				DependsOn: []string{},
				HealthCheck: models.HealthCheckConfig{
					Endpoint: "/api/v3/health",
					Port:     8989,
					Timeout:  10,
					Retries:  3,
				},
			},
		},
		GlobalSettings: models.GlobalSettings{
			DataDirectory:   "~/media",
			ConfigDirectory: "~/.config/mctl/apps",
			BackupDirectory: "~/media/backups",
			Timezone:        "UTC",
		},
		Observability: models.ObservabilityConfig{
			LogLevel:            "info",
			LogDirectory:        "~/.local/share/mctl/logs",
			MetricsEnabled:      true,
			HealthCheckInterval: 30,
		},
	}
}