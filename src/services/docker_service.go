package services

import (
	"context"
	"fmt"
	"io"
	"time"

	"github.com/docker/docker/api/types"
	"github.com/docker/docker/api/types/container"
	"github.com/docker/docker/api/types/image"
	"github.com/docker/docker/api/types/network"
	"github.com/docker/go-connections/nat"
	"github.com/docker/docker/client"
	"github.com/idriesalbender/media-center-v3/src/models"
)

// DockerService handles Docker-related operations for the ARR-stack
type DockerService struct {
	client *client.Client
}

// NewDockerService creates a new DockerService instance
func NewDockerService() (*DockerService, error) {
	// Create a new Docker client using the Docker SDK
	cli, err := client.NewClientWithOpts(client.FromEnv)
	if err != nil {
		return nil, fmt.Errorf("failed to create Docker client: %w", err)
	}
	
	return &DockerService{
		client: cli,
	}, nil
}

// StartApplication starts a single application container
func (s *DockerService) StartApplication(ctx context.Context, appConfig *models.ApplicationConfig) (*models.ApplicationState, error) {
	// Pull the Docker image if it doesn't exist
	if err := s.pullImage(ctx, appConfig.Image); err != nil {
		return nil, fmt.Errorf("failed to pull image %s: %w", appConfig.Image, err)
	}

	// Create and start the container with the specified configuration
	containerID, err := s.createContainer(ctx, appConfig)
	if err != nil {
		return nil, fmt.Errorf("failed to create container for %s: %w", appConfig.Name, err)
	}

	// Start the container
	if err := s.startContainer(ctx, containerID); err != nil {
		return nil, fmt.Errorf("failed to start container %s for %s: %w", containerID, appConfig.Name, err)
	}

	// Return the application state with container ID
	return &models.ApplicationState{
		Name:         appConfig.Name,
		Status:       "running",
		ContainerID:  containerID,
		LastUpdated:  time.Now(),
		ErrorMessage: "",
	}, nil
}

// StopApplication stops a single application container
func (s *DockerService) StopApplication(ctx context.Context, appState *models.ApplicationState) error {
	if appState.ContainerID == "" {
		return fmt.Errorf("no container ID for application %s", appState.Name)
	}

	// Stop the container with the specified container ID
	timeout := 30
	stopOptions := container.StopOptions{
		Timeout: &timeout,
	}
	if err := s.client.ContainerStop(ctx, appState.ContainerID, stopOptions); err != nil {
		return fmt.Errorf("failed to stop container %s for %s: %w", appState.ContainerID, appState.Name, err)
	}

	// Update the application state
	appState.Status = "stopped"
	appState.LastUpdated = time.Now()
	appState.ContainerID = ""

	return nil
}

// GetApplicationStatus retrieves the current status of an application
func (s *DockerService) GetApplicationStatus(ctx context.Context, appName string) (*models.ApplicationState, error) {
	// Query Docker for containers with the specified name
	containers, err := s.client.ContainerList(ctx, container.ListOptions{
		All: true,
	})
	if err != nil {
		return nil, fmt.Errorf("failed to list containers for %s: %w", appName, err)
	}

	// Find container with matching name
	var containerInfo types.Container
	found := false
	for _, c := range containers {
		for _, name := range c.Names {
			if name == "/"+appName {
				containerInfo = c
				found = true
				break
			}
		}
		if found {
			break
		}
	}

	// If no containers found, return stopped state
	if !found {
		return &models.ApplicationState{
			Name:        appName,
			Status:      "stopped",
			ContainerID: "",
			LastUpdated: time.Now(),
		}, nil
	}

	// Determine the container's current status
	status := "unknown"
	switch containerInfo.State {
	case "running":
		status = "running"
	case "exited":
		status = "stopped"
	case "paused":
		status = "paused"
	default:
		status = containerInfo.State
	}

	// Return the application state with current status
	return &models.ApplicationState{
		Name:        appName,
		Status:      status,
		ContainerID: containerInfo.ID,
		LastUpdated: time.Now(),
	}, nil
}

// StartStack starts all applications in the stack in dependency order
func (s *DockerService) StartStack(ctx context.Context, config *models.ARRStackConfiguration) ([]*models.ApplicationState, error) {
	// For now, we'll start all enabled applications without dependency ordering
	// A more complete implementation would resolve dependencies first
	
	var appStates []*models.ApplicationState
	
	for _, appConfig := range config.Applications {
		if !appConfig.Enabled {
			continue
		}
		
		// Start the application
		appState, err := s.StartApplication(ctx, &appConfig)
		if err != nil {
			// If one application fails to start, we'll continue with the others
			// but record the error in the state
			appStates = append(appStates, &models.ApplicationState{
				Name:         appConfig.Name,
				Status:       "error",
				ContainerID:  "",
				LastUpdated:  time.Now(),
				ErrorMessage: err.Error(),
			})
			continue
		}
		
		appStates = append(appStates, appState)
	}
	
	return appStates, nil
}

// StopStack stops all applications in the stack
func (s *DockerService) StopStack(ctx context.Context, appStates []*models.ApplicationState) error {
	// Stop all applications
	for _, appState := range appStates {
		if appState.Status == "running" && appState.ContainerID != "" {
			if err := s.StopApplication(ctx, appState); err != nil {
				// Log error but continue stopping other applications
				fmt.Printf("Warning: failed to stop %s: %v\n", appState.Name, err)
				appState.Status = "error"
				appState.ErrorMessage = err.Error()
			}
		}
	}
	
	return nil
}

// ConnectToDockerDaemon attempts to connect to the Docker daemon
func (s *DockerService) ConnectToDockerDaemon(ctx context.Context) error {
	// Ping the Docker daemon to verify connectivity
	_, err := s.client.Ping(ctx)
	if err != nil {
		return fmt.Errorf("failed to ping Docker daemon: %w", err)
	}
	return nil
}

// pullImage pulls a Docker image if it doesn't exist locally
func (s *DockerService) pullImage(ctx context.Context, imageName string) error {
	// Check if the image exists locally
	_, _, err := s.client.ImageInspectWithRaw(ctx, imageName)
	if err == nil {
		// Image already exists locally
		return nil
	}
	
	// If the image doesn't exist, pull it
	out, err := s.client.ImagePull(ctx, imageName, image.PullOptions{})
	if err != nil {
		return fmt.Errorf("failed to pull image %s: %w", imageName, err)
	}
	defer out.Close()
	
	// Read the output to complete the pull operation
	_, err = io.ReadAll(out)
	if err != nil {
		return fmt.Errorf("failed to read image pull output: %w", err)
	}
	
	return nil
}

// createContainer creates a Docker container based on the application configuration
func (s *DockerService) createContainer(ctx context.Context, appConfig *models.ApplicationConfig) (string, error) {
	// Convert the ApplicationConfig to Docker container configuration
	config := &container.Config{
		Image:        appConfig.Image,
		Env:          s.convertEnvMapToSlice(appConfig.Environment),
		ExposedPorts: s.convertPortMappingsToPortSet(appConfig.Ports),
	}
	
	hostConfig := &container.HostConfig{
		PortBindings: s.convertPortMappingsToPortMap(appConfig.Ports),
		Binds:        s.convertVolumeMappingsToBinds(appConfig.Volumes),
	}
	
	networkingConfig := &network.NetworkingConfig{}
	
	// Create the container using the Docker SDK
	resp, err := s.client.ContainerCreate(ctx, config, hostConfig, networkingConfig, nil, appConfig.Name)
	if err != nil {
		return "", fmt.Errorf("failed to create container for %s: %w", appConfig.Name, err)
	}
	
	return resp.ID, nil
}

// startContainer starts a Docker container
func (s *DockerService) startContainer(ctx context.Context, containerID string) error {
	// Start the container using the Docker SDK
	if err := s.client.ContainerStart(ctx, containerID, container.StartOptions{}); err != nil {
		return fmt.Errorf("failed to start container %s: %w", containerID, err)
	}
	
	return nil
}

// convertEnvMapToSlice converts environment map to slice of strings
func (s *DockerService) convertEnvMapToSlice(envMap map[string]string) []string {
	var envSlice []string
	for key, value := range envMap {
		envSlice = append(envSlice, fmt.Sprintf("%s=%s", key, value))
	}
	return envSlice
}

// convertPortMappingsToPortSet converts port mappings to exposed ports
func (s *DockerService) convertPortMappingsToPortSet(ports []models.PortMapping) nat.PortSet {
	portSet := nat.PortSet{}
	for _, portMapping := range ports {
		port := nat.Port(fmt.Sprintf("%d/%s", portMapping.ContainerPort, portMapping.Protocol))
		portSet[port] = struct{}{}
	}
	return portSet
}

// convertPortMappingsToPortMap converts port mappings to port bindings
func (s *DockerService) convertPortMappingsToPortMap(ports []models.PortMapping) nat.PortMap {
	portMap := nat.PortMap{}
	for _, portMapping := range ports {
		containerPort := nat.Port(fmt.Sprintf("%d/%s", portMapping.ContainerPort, portMapping.Protocol))
		hostBinding := []nat.PortBinding{
			{
				HostIP:   "0.0.0.0",
				HostPort: fmt.Sprintf("%d", portMapping.HostPort),
			},
		}
		portMap[containerPort] = hostBinding
	}
	return portMap
}

// convertVolumeMappingsToBinds converts volume mappings to bind mounts
func (s *DockerService) convertVolumeMappingsToBinds(volumes []models.VolumeMapping) []string {
	var binds []string
	for _, volumeMapping := range volumes {
		mode := "rw"
		if volumeMapping.ReadOnly {
			mode = "ro"
		}
		binds = append(binds, fmt.Sprintf("%s:%s:%s", volumeMapping.HostPath, volumeMapping.ContainerPath, mode))
	}
	return binds
}