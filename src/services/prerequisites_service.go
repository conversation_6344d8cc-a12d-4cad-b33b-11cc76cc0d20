package services

import (
	"context"
	"fmt"
	"os/exec"
	"runtime"

	"github.com/docker/docker/client"
	"github.com/idriesalbender/media-center-v3/src/models"
	"github.com/shirou/gopsutil/v3/disk"
	"github.com/shirou/gopsutil/v3/mem"
)

// PrerequisitesService checks system prerequisites for running the ARR-stack
type PrerequisitesService struct {
	dockerClient *client.Client
}

// NewPrerequisitesService creates a new PrerequisitesService instance
func NewPrerequisitesService() (*PrerequisitesService, error) {
	// Try to create a Docker client
	cli, err := client.NewClientWithOpts(client.FromEnv)
	if err != nil {
		// If we can't create a client, we'll set it to nil and check later
		return &PrerequisitesService{
			dockerClient: nil,
		}, nil
	}

	return &PrerequisitesService{
		dockerClient: cli,
	}, nil
}

// CheckPrerequisites verifies that all system prerequisites are met
func (s *PrerequisitesService) CheckPrerequisites() (*models.Prerequisites, error) {
	// Check if Docker is installed
	dockerInstalled := s.isDockerInstalled()

	// Check if Docker daemon is running
	dockerRunning := s.isDockerRunning()

	// Check if Docker Compose is available
	dockerComposeAvailable := s.isDockerComposeAvailable()

	// Get system resources
	systemResources, err := s.GetSystemResources()
	if err != nil {
		systemResources = &models.SystemResources{
			TotalMemoryMB:   0,
			AvailableMemoryMB: 0,
			TotalDiskGB:     0,
			AvailableDiskGB: 0,
		}
	}

	return &models.Prerequisites{
		DockerInstalled:        dockerInstalled,
		DockerRunning:          dockerRunning,
		DockerComposeAvailable: dockerComposeAvailable,
		SystemResources:       *systemResources,
	}, nil
}

// GetDockerVersion retrieves the version of Docker installed
func (s *PrerequisitesService) GetDockerVersion() (string, error) {
	if s.dockerClient == nil {
		return "", fmt.Errorf("Docker client not available")
	}

	// Query the Docker daemon for its version
	ctx := context.Background()
	version, err := s.dockerClient.ServerVersion(ctx)
	if err != nil {
		return "", fmt.Errorf("failed to get Docker version: %w", err)
	}

	return version.Version, nil
}

// GetDockerComposeVersion retrieves the version of Docker Compose installed
func (s *PrerequisitesService) GetDockerComposeVersion() (string, error) {
	// Query Docker Compose for its version
	cmd := exec.Command("docker-compose", "--version")
	output, err := cmd.Output()
	if err != nil {
		// Try docker compose (newer version)
		cmd = exec.Command("docker", "compose", "--version")
		output, err = cmd.Output()
		if err != nil {
			return "", fmt.Errorf("failed to get Docker Compose version: %w", err)
		}
	}

	return string(output), nil
}

// GetSystemResources retrieves information about system resources
func (s *PrerequisitesService) GetSystemResources() (*models.SystemResources, error) {
	// Get memory information
	vmStat, err := mem.VirtualMemory()
	if err != nil {
		return nil, fmt.Errorf("failed to get memory information: %w", err)
	}

	// Get disk information (use root path for now)
	diskStat, err := disk.Usage("/")
	if err != nil {
		return nil, fmt.Errorf("failed to get disk information: %w", err)
	}

	return &models.SystemResources{
		TotalMemoryMB:   int(vmStat.Total / 1024 / 1024),
		AvailableMemoryMB: int(vmStat.Available / 1024 / 1024),
		TotalDiskGB:     int(diskStat.Total / 1024 / 1024 / 1024),
		AvailableDiskGB: int(diskStat.Free / 1024 / 1024 / 1024),
	}, nil
}

// isDockerInstalled checks if Docker is installed
func (s *PrerequisitesService) isDockerInstalled() bool {
	var cmd *exec.Cmd
	switch runtime.GOOS {
	case "windows":
		cmd = exec.Command("where", "docker")
	default:
		cmd = exec.Command("which", "docker")
	}

	if err := cmd.Run(); err != nil {
		return false
	}

	return true
}

// isDockerRunning checks if Docker daemon is running
func (s *PrerequisitesService) isDockerRunning() bool {
	if s.dockerClient == nil {
		return false
	}

	// Ping the Docker daemon to verify connectivity
	ctx := context.Background()
	_, err := s.dockerClient.Ping(ctx)
	return err == nil
}

// isDockerComposeAvailable checks if Docker Compose is available
func (s *PrerequisitesService) isDockerComposeAvailable() bool {
	// Try docker-compose first
	cmd := exec.Command("docker-compose", "--version")
	if err := cmd.Run(); err != nil {
		// Try docker compose (newer version)
		cmd = exec.Command("docker", "compose", "--version")
		if err := cmd.Run(); err != nil {
			return false
		}
	}

	return true
}