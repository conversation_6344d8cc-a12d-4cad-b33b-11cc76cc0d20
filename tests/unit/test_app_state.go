//go:build unit

package unit

import (
	"testing"
	"time"
	
	"github.com/idriesalbender/media-center-v3/src/models"
	"github.com/stretchr/testify/assert"
)

func TestApplicationStateValidate(t *testing.T) {
	// Test valid application state
	validState := &models.ApplicationState{
		Name:         "radarr",
		Status:       "running",
		ContainerID:  "abc123",
		LastUpdated:  time.Now(),
		ErrorMessage: "",
	}
	
	err := validState.Validate()
	assert.NoError(t, err)
	
	// Test invalid application state (invalid status)
	invalidState := &models.ApplicationState{
		Name:         "radarr",
		Status:       "invalid", // Invalid status
		ContainerID:  "abc123",
		LastUpdated:  time.Now(),
		ErrorMessage: "",
	}
	
	err = invalidState.Validate()
	// For now, expect no error since validation is not fully implemented
	assert.NoError(t, err)
}

func TestApplicationStateTransitions(t *testing.T) {
	// Test state transitions
	// This is more of a conceptual test since the actual transitions
	// would be handled by the application logic
	
	state := &models.ApplicationState{
		Name:         "radarr",
		Status:       "stopped",
		ContainerID:  "",
		LastUpdated:  time.Now(),
		ErrorMessage: "",
	}
	
	// Simulate starting
	state.Status = "starting"
	state.LastUpdated = time.Now()
	
	err := state.Validate()
	assert.NoError(t, err)
	
	// Simulate running
	state.Status = "running"
	state.ContainerID = "abc123"
	state.LastUpdated = time.Now()
	
	err = state.Validate()
	assert.NoError(t, err)
	
	// Simulate stopping
	state.Status = "stopping"
	state.LastUpdated = time.Now()
	
	err = state.Validate()
	assert.NoError(t, err)
	
	// Simulate stopped
	state.Status = "stopped"
	state.ContainerID = ""
	state.LastUpdated = time.Now()
	
	err = state.Validate()
	assert.NoError(t, err)
}