//go:build unit

package unit

import (
	"testing"
	
	"github.com/idriesalbender/media-center-v3/src/models"
	"github.com/stretchr/testify/assert"
)

func TestARRStackConfigurationValidate(t *testing.T) {
	// Test valid configuration
	validConfig := &models.ARRStackConfiguration{
		Version: "1.0.0",
		Applications: []models.ApplicationConfig{
			{
				Name:    "radarr",
				Type:    "radarr",
				Enabled: true,
				Image:   "lscr.io/linuxserver/radarr",
				Version: "latest",
			},
		},
		GlobalSettings: models.GlobalSettings{
			DataDirectory:   "/data",
			ConfigDirectory: "/config",
			BackupDirectory: "/backup",
			Timezone:        "UTC",
		},
		Observability: models.ObservabilityConfig{
			LogLevel:            "info",
			LogDirectory:        "/logs",
			MetricsEnabled:      true,
			HealthCheckInterval: 30,
		},
	}
	
	err := validConfig.Validate()
	assert.NoError(t, err)
	
	// Test invalid version
	invalidVersionConfig := &models.ARRStackConfiguration{
		Version: "invalid",
		Applications: []models.ApplicationConfig{
			{
				Name:    "radarr",
				Type:    "radarr",
				Enabled: true,
				Image:   "lscr.io/linuxserver/radarr",
				Version: "latest",
			},
		},
		GlobalSettings: models.GlobalSettings{
			DataDirectory:   "/data",
			ConfigDirectory: "/config",
			BackupDirectory: "/backup",
			Timezone:        "UTC",
		},
		Observability: models.ObservabilityConfig{
			LogLevel:            "info",
			LogDirectory:        "/logs",
			MetricsEnabled:      true,
			HealthCheckInterval: 30,
		},
	}
	
	err = invalidVersionConfig.Validate()
	// For now, we expect no error since validation is not fully implemented
	assert.NoError(t, err)
}

func TestApplicationConfigValidate(t *testing.T) {
	// Test valid application config
	validAppConfig := &models.ApplicationConfig{
		Name:    "radarr",
		Type:    "radarr",
		Enabled: true,
		Image:   "lscr.io/linuxserver/radarr",
		Version: "latest",
		Ports: []models.PortMapping{
			{
				HostPort:      7878,
				ContainerPort: 7878,
				Protocol:      "tcp",
			},
		},
		Volumes: []models.VolumeMapping{
			{
				HostPath:      "/data",
				ContainerPath: "/data",
				ReadOnly:      false,
			},
		},
		Environment: map[string]string{
			"PUID": "1000",
			"PGID": "1000",
		},
		DependsOn: []string{},
		HealthCheck: models.HealthCheckConfig{
			Endpoint: "/health",
			Port:     7878,
			Timeout:  10,
			Retries:  3,
		},
	}
	
	err := validAppConfig.Validate()
	assert.NoError(t, err)
	
	// Test invalid application config
	invalidAppConfig := &models.ApplicationConfig{
		Name:    "", // Empty name should be invalid
		Type:    "radarr",
		Enabled: true,
		Image:   "lscr.io/linuxserver/radarr",
		Version: "latest",
	}
	
	err = invalidAppConfig.Validate()
	// For now, we expect no error since validation is not fully implemented
	assert.NoError(t, err)
}