//go:build unit

package unit

import (
	"testing"
	
	"github.com/idriesalbender/media-center-v3/src/models"
	"github.com/idriesalbender/media-center-v3/src/services"
	"github.com/stretchr/testify/assert"
)

func TestDockerServiceNewDockerService(t *testing.T) {
	// Test creating a new Docker service
	dockerService, err := services.NewDockerService()
	
	// For now, expect it to not fail since we're in a test environment
	assert.NoError(t, err)
	
	// Use the variable to avoid "declared and not used" error
	_ = dockerService
}

func TestDockerServiceResolveDependencies(t *testing.T) {
	// Test dependency resolution
	appStateService := services.NewAppStateService()
	
	// Test with no dependencies
	apps := []models.ApplicationConfig{
		{
			Name: "radarr",
		},
		{
			Name: "sonarr",
		},
	}
	
	result, err := appStateService.ResolveDependencies(apps)
	assert.NoError(t, err)
	assert.Len(t, result, 2)
	
	// Test with simple dependencies
	apps = []models.ApplicationConfig{
		{
			Name:      "radarr",
			DependsOn: []string{"sonarr"},
		},
		{
			Name: "sonarr",
		},
	}
	
	result, err = appStateService.ResolveDependencies(apps)
	assert.NoError(t, err)
	assert.Len(t, result, 2)
	
	// Check that sonarr comes before radarr (dependency order)
	assert.Equal(t, "sonarr", result[0].Name)
	assert.Equal(t, "radarr", result[1].Name)
	
	// Test with circular dependencies
	apps = []models.ApplicationConfig{
		{
			Name:      "radarr",
			DependsOn: []string{"sonarr"},
		},
		{
			Name:      "sonarr",
			DependsOn: []string{"radarr"}, // Circular dependency
		},
	}
	
	result, err = appStateService.ResolveDependencies(apps)
	assert.Error(t, err)
	assert.Nil(t, result)
	assert.Contains(t, err.Error(), "circular dependency")
}